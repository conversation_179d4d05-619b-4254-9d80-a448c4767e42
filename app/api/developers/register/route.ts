import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { generateOTPWithExpiry } from '@/lib/otp-utils';
import { sendOTPEmail } from '@/lib/email-service';
import { setOTP, getUser, createUser } from '@/lib/storage';
import bcrypt from 'bcryptjs';

// Validation schema for developer registration
const registerSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters long'),
});

export async function POST(request: NextRequest) {
  try {
    let body;
    
    // Safely parse request body
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return NextResponse.json({
        success: false,
        message: 'Invalid JSON in request body',
      }, { status: 400 });
    }

    // Check if body exists
    if (!body || typeof body !== 'object') {
      return NextResponse.json({
        success: false,
        message: 'Request body is required',
      }, { status: 400 });
    }

    // Validate request body
    const validatedData = registerSchema.parse(body);
    const { name, email, password } = validatedData;

    // Check if developer already exists
    const existingDeveloper = getUser(email);
    
    if (existingDeveloper) {
      return NextResponse.json({
        success: false,
        message: 'Developer already exists',
      }, { status: 409 });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create new developer (inactive until verified)
    const developer = createUser({
      email,
      password: hashedPassword,
      isVerified: false,
      isActive: false,
      socialType: 'email',
      createdAt: new Date().toISOString(),
    });

    // Generate OTP for email verification
    const { otp, expiry } = generateOTPWithExpiry(10); // 10 minutes expiry
    
    // Store OTP
    setOTP(email, { otp, expiry, email });
    
    // Send OTP email
    try {
      await sendOTPEmail(email, otp);
    } catch (emailError) {
      console.error('Failed to send OTP email:', emailError);
      // Continue without failing - OTP is still stored
    }

    // Return success response (matching Swagger spec)
    return NextResponse.json({
      id: developer.id,
      name: name, // Note: We should store name in the user object
      email: developer.email,
      isActive: developer.isActive,
      createdAt: developer.createdAt,
    }, { status: 201 });

  } catch (error) {
    console.error('Error in developer registration:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        message: error.errors[0]?.message || 'Validation error',
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      message: 'Internal server error',
    }, { status: 500 });
  }
}
