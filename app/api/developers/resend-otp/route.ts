import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { generateOTPWithExpiry } from '@/lib/otp-utils';
import { sendOTPEmail } from '@/lib/email-service';
import { setOTP, getUser } from '@/lib/storage';

// Validation schema for resend OTP
const resendOTPSchema = z.object({
  email: z.string().email('Invalid email format'),
});

export async function POST(request: NextRequest) {
  try {
    let body;
    
    // Safely parse request body
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return NextResponse.json({
        success: false,
        message: 'Invalid JSON in request body',
      }, { status: 400 });
    }

    // Check if body exists
    if (!body || typeof body !== 'object') {
      return NextResponse.json({
        success: false,
        message: 'Request body is required',
      }, { status: 400 });
    }

    // Validate request body
    const validatedData = resendOTPSchema.parse(body);
    const { email } = validatedData;

    // Check if developer exists
    const developer = getUser(email);
    
    if (!developer) {
      return NextResponse.json({
        success: false,
        message: 'Developer not found',
      }, { status: 404 });
    }

    // Check if developer is already verified
    if (developer.isVerified && developer.isActive) {
      return NextResponse.json({
        success: false,
        message: 'Account already verified',
      }, { status: 409 });
    }

    // Generate new OTP
    const { otp, expiry } = generateOTPWithExpiry(10); // 10 minutes expiry
    
    // Store OTP
    setOTP(email, { otp, expiry, email });
    
    // Send OTP email
    try {
      await sendOTPEmail(email, otp);
      
      return NextResponse.json({
        success: true,
        message: 'OTP resent successfully',
      }, { status: 200 });
      
    } catch (emailError) {
      console.error('Failed to send OTP email:', emailError);
      
      return NextResponse.json({
        success: false,
        message: 'Failed to send OTP email. Please try again.',
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in resend OTP:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        message: error.errors[0]?.message || 'Validation error',
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      message: 'Internal server error',
    }, { status: 500 });
  }
}
