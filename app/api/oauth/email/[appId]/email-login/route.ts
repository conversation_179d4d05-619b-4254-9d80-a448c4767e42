import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { generateOTPWithExpiry } from '@/lib/otp-utils';
import { sendOTPEmail } from '@/lib/email-service';
import { setOTP, getUser, createUser, updateUser } from '@/lib/storage';
import jwt from 'jsonwebtoken';

// Validation schema for email login
const emailLoginSchema = z.object({
  email: z.string().email('Invalid email format'),
});

// JWT secret for token generation
const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret-key';

interface WalletData {
  walletAddress: string;
  publicKey: string;
  socialType: 'email';
  userData: string;
}

export async function POST(
  request: NextRequest,
  { params }: { params: { appId: string } }
) {
  try {
    const { appId } = params;
    
    // Validate appId
    if (!appId || typeof appId !== 'string') {
      return NextResponse.json({
        success: false,
        message: 'Invalid app ID',
      }, { status: 400 });
    }

    let body;
    
    // Safely parse request body
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return NextResponse.json({
        success: false,
        message: 'Invalid JSON in request body',
      }, { status: 400 });
    }

    // Validate request body
    const validatedData = emailLoginSchema.parse(body);
    const { email } = validatedData;

    // Check for existing JWT token in Authorization header
    const authHeader = request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      try {
        const decoded = jwt.verify(token, JWT_SECRET) as any;
        const user = getUser(decoded.email);
        
        if (user && user.email === email && user.isActive) {
          // Valid token exists, return wallet details
          const walletData: WalletData = {
            walletAddress: user.walletAddress || generateWalletAddress(),
            publicKey: user.publicKey || generatePublicKey(),
            socialType: 'email',
            userData: JSON.stringify({ email: user.email }),
          };

          return NextResponse.json({
            success: true,
            message: 'User already logged in',
            data: walletData,
            isActive: true,
            token: token,
          }, { status: 200 });
        }
      } catch (jwtError) {
        // Invalid token, continue with normal flow
        console.log('Invalid JWT token, proceeding with OTP flow');
      }
    }

    // Check if user exists
    const existingUser = getUser(email);
    
    if (existingUser && existingUser.isActive) {
      // User exists and is active, send OTP for verification
      const { otp, expiry } = generateOTPWithExpiry(10); // 10 minutes expiry
      
      // Store OTP
      setOTP(email, { otp, expiry, email });
      
      // Send OTP email
      try {
        await sendOTPEmail(email, otp);
      } catch (emailError) {
        console.error('Failed to send OTP email:', emailError);
        // Continue without failing - OTP is still stored
      }

      return NextResponse.json({
        success: true,
        message: 'OTP sent to email for verification',
        isActive: false,
        walletExists: true,
      }, { status: 201 });
    } else {
      // New wallet registration
      const { otp, expiry } = generateOTPWithExpiry(10);
      
      // Store OTP for new registration
      setOTP(email, { otp, expiry, email });
      
      // Send OTP email
      try {
        await sendOTPEmail(email, otp);
      } catch (emailError) {
        console.error('Failed to send OTP email:', emailError);
        // Continue without failing - OTP is still stored
      }

      return NextResponse.json({
        success: true,
        message: 'OTP sent to email for verification',
        isActive: false,
        walletExists: false,
      }, { status: 201 });
    }

  } catch (error) {
    console.error('Error in email login:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        message: error.errors[0]?.message || 'Validation error',
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      message: 'Internal server error',
    }, { status: 500 });
  }
}

// Helper functions to generate wallet data (mock implementation)
function generateWalletAddress(): string {
  // Generate a mock Ethereum address
  return '0x' + Array.from({ length: 40 }, () => 
    Math.floor(Math.random() * 16).toString(16)
  ).join('');
}

function generatePublicKey(): string {
  // Generate a mock public key
  return '0x04' + Array.from({ length: 128 }, () => 
    Math.floor(Math.random() * 16).toString(16)
  ).join('');
}
