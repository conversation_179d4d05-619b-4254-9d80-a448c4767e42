import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { generateOTPWithExpiry } from '@/lib/otp-utils';
import { sendOTPEmail } from '@/lib/email-service';
import { setOTP, getUser } from '@/lib/storage';

// Validation schema for resend OTP
const resendOTPSchema = z.object({
  email: z.string().email('Invalid email format'),
});

export async function POST(
  request: NextRequest,
  { params }: { params: { appId: string } }
) {
  try {
    const { appId } = params;
    
    // Validate appId
    if (!appId || typeof appId !== 'string') {
      return NextResponse.json({
        success: false,
        message: 'Invalid app ID',
      }, { status: 400 });
    }

    let body;
    
    // Safely parse request body
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return NextResponse.json({
        success: false,
        message: 'Invalid JSON in request body',
      }, { status: 400 });
    }

    // Validate request body
    const validatedData = resendOTPSchema.parse(body);
    const { email } = validatedData;

    // Check if user exists (for existing wallet verification)
    // Note: According to Swagger, this endpoint is for wallet verification,
    // so we should check if there's a pending verification process
    const user = getUser(email);
    
    // For this endpoint, we'll allow resending OTP for both existing and new users
    // as it's part of the wallet verification flow
    
    // Generate new OTP
    const { otp, expiry } = generateOTPWithExpiry(10); // 10 minutes expiry
    
    // Store OTP
    setOTP(email, { otp, expiry, email });
    
    // Send OTP email
    try {
      await sendOTPEmail(email, otp);
      
      return NextResponse.json({
        success: true,
        message: 'OTP resent to email for verification',
      }, { status: 200 });
      
    } catch (emailError) {
      console.error('Failed to send OTP email:', emailError);
      
      return NextResponse.json({
        success: false,
        message: 'Failed to send OTP email. Please try again.',
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in resend OTP:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        message: error.errors[0]?.message || 'Validation error',
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      message: 'Internal server error',
    }, { status: 500 });
  }
}
