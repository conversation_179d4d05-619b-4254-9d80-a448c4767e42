import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getOTP, deleteOTP, getUser, createUser, updateUser } from '@/lib/storage';
import { isOTPExpired } from '@/lib/otp-utils';
import jwt from 'jsonwebtoken';

// Validation schema for wallet verification
const walletVerifySchema = z.object({
  email: z.string().email('Invalid email format'),
  otp: z.string().length(6, 'OTP must be 6 digits'),
});

// JWT secret for token generation
const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret-key';

interface WalletData {
  walletAddress: string;
  publicKey: string;
  socialType: 'email';
  userData: string;
}

export async function POST(
  request: NextRequest,
  { params }: { params: { appId: string } }
) {
  try {
    const { appId } = params;
    
    // Validate appId
    if (!appId || typeof appId !== 'string') {
      return NextResponse.json({
        success: false,
        message: 'Invalid app ID',
      }, { status: 400 });
    }

    let body;
    
    // Safely parse request body
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return NextResponse.json({
        success: false,
        message: 'Invalid JSON in request body',
      }, { status: 400 });
    }

    // Validate request body
    const validatedData = walletVerifySchema.parse(body);
    const { email, otp } = validatedData;

    // Check for existing JWT token in Authorization header
    const authHeader = request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      try {
        const decoded = jwt.verify(token, JWT_SECRET) as any;
        const user = getUser(decoded.email);
        
        if (user && user.email === email && user.isActive) {
          // Valid token exists and matches the wallet, return wallet details
          const walletData: WalletData = {
            walletAddress: user.walletAddress || generateWalletAddress(),
            publicKey: user.publicKey || generatePublicKey(),
            socialType: 'email',
            userData: JSON.stringify({ email: user.email }),
          };

          return NextResponse.json({
            success: true,
            message: 'Operation successful',
            isActive: true,
            walletExists: true,
            data: walletData,
            token: token,
          }, { status: 200 });
        }
      } catch (jwtError) {
        // Invalid token, continue with OTP verification
        console.log('Invalid JWT token, proceeding with OTP verification');
      }
    }

    // Get stored OTP
    const storedOTPData = getOTP(email);
    
    if (!storedOTPData) {
      return NextResponse.json({
        success: false,
        message: 'OTP not found or expired. Please request a new OTP.',
      }, { status: 400 });
    }

    // Check if OTP has expired
    if (isOTPExpired(storedOTPData.expiry)) {
      deleteOTP(email);
      return NextResponse.json({
        success: false,
        message: 'OTP has expired. Please request a new OTP.',
      }, { status: 401 });
    }

    // Verify OTP
    if (storedOTPData.otp !== otp) {
      return NextResponse.json({
        success: false,
        message: 'Invalid OTP. Please check and try again.',
      }, { status: 401 });
    }

    // OTP is valid, clean it up
    deleteOTP(email);

    // Check if user exists
    let user = getUser(email);
    const walletExists = !!user;

    if (!user) {
      // Create new user/wallet
      const walletAddress = generateWalletAddress();
      const publicKey = generatePublicKey();
      
      user = createUser({
        email,
        walletAddress,
        publicKey,
        socialType: 'email',
        isActive: true,
        createdAt: new Date().toISOString(),
      });
    } else {
      // Update existing user to active
      user = updateUser(email, {
        isActive: true,
        walletAddress: user.walletAddress || generateWalletAddress(),
        publicKey: user.publicKey || generatePublicKey(),
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        email: user.email, 
        walletAddress: user.walletAddress,
        socialType: 'email' 
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Prepare wallet data response
    const walletData: WalletData = {
      walletAddress: user.walletAddress!,
      publicKey: user.publicKey!,
      socialType: 'email',
      userData: JSON.stringify({ email: user.email }),
    };

    return NextResponse.json({
      success: true,
      message: 'Operation successful',
      isActive: true,
      walletExists,
      data: walletData,
      token,
    }, { status: 200 });

  } catch (error) {
    console.error('Error in wallet verification:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        message: error.errors[0]?.message || 'Validation error',
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      message: 'Internal server error',
    }, { status: 500 });
  }
}

// Helper functions to generate wallet data (mock implementation)
function generateWalletAddress(): string {
  // Generate a mock Ethereum address
  return '0x' + Array.from({ length: 40 }, () => 
    Math.floor(Math.random() * 16).toString(16)
  ).join('');
}

function generatePublicKey(): string {
  // Generate a mock public key
  return '0x04' + Array.from({ length: 128 }, () => 
    Math.floor(Math.random() * 16).toString(16)
  ).join('');
}
