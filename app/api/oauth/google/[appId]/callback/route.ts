import { NextRequest, NextResponse } from 'next/server';
import { getStoredState, deleteStoredState } from '../initiate-google-auth/route';
import { createUser, updateUser, getUser } from '@/lib/storage';
import jwt from 'jsonwebtoken';

// Google OAuth configuration
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID || 'your-google-client-id';
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || 'your-google-client-secret';
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret-key';

interface GoogleTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  id_token: string;
  refresh_token?: string;
}

interface GoogleUserInfo {
  id: string;
  email: string;
  verified_email: boolean;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { appId: string } }
) {
  try {
    const { appId } = params;
    const { searchParams } = new URL(request.url);
    
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    // Check for OAuth errors
    if (error) {
      console.error('Google OAuth error:', error);
      return NextResponse.json({
        success: false,
        message: `Google authentication failed: ${error}`,
      }, { status: 401 });
    }

    // Validate required parameters
    if (!code || !state) {
      return NextResponse.json({
        success: false,
        message: 'Missing required OAuth parameters',
      }, { status: 400 });
    }

    // Verify state to prevent CSRF attacks
    const storedStateData = getStoredState(state);
    if (!storedStateData || storedStateData.appId !== appId) {
      return NextResponse.json({
        success: false,
        message: 'Invalid state parameter',
      }, { status: 401 });
    }

    // Clean up state
    deleteStoredState(state);

    // Exchange code for tokens
    const tokenResponse = await exchangeCodeForTokens(code, appId);
    if (!tokenResponse) {
      return NextResponse.json({
        success: false,
        message: 'Failed to exchange code for tokens',
      }, { status: 401 });
    }

    // Get user info from Google
    const userInfo = await getGoogleUserInfo(tokenResponse.access_token);
    if (!userInfo) {
      return NextResponse.json({
        success: false,
        message: 'Failed to get user information from Google',
      }, { status: 401 });
    }

    // Check if email is verified
    if (!userInfo.verified_email) {
      return NextResponse.json({
        success: false,
        message: 'Google email is not verified',
      }, { status: 401 });
    }

    // Check if user exists or create new one
    let user = getUser(userInfo.email);
    
    if (!user) {
      // Create new user with Google data
      const walletAddress = generateWalletAddress();
      const publicKey = generatePublicKey();
      
      user = createUser({
        email: userInfo.email,
        walletAddress,
        publicKey,
        socialType: 'google',
        isActive: true,
        createdAt: new Date().toISOString(),
        googleData: {
          id: userInfo.id,
          name: userInfo.name,
          picture: userInfo.picture,
        },
      });
    } else {
      // Update existing user
      user = updateUser(userInfo.email, {
        isActive: true,
        walletAddress: user.walletAddress || generateWalletAddress(),
        publicKey: user.publicKey || generatePublicKey(),
        socialType: 'google',
        googleData: {
          id: userInfo.id,
          name: userInfo.name,
          picture: userInfo.picture,
        },
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        email: user.email, 
        walletAddress: user.walletAddress,
        socialType: 'google',
        googleId: userInfo.id,
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Store token for frontend retrieval (in production, use secure session storage)
    storeAuthResult(appId, {
      success: true,
      token,
      user: {
        email: user.email,
        walletAddress: user.walletAddress!,
        publicKey: user.publicKey!,
        socialType: 'google',
        userData: JSON.stringify({
          email: user.email,
          name: userInfo.name,
          picture: userInfo.picture,
        }),
      },
    });

    // Redirect to success page or close popup
    const redirectUrl = `${BASE_URL}/auth/google-callback?success=true&appId=${appId}`;
    return NextResponse.redirect(redirectUrl, { status: 302 });

  } catch (error) {
    console.error('Error in Google OAuth callback:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
    }, { status: 500 });
  }
}

async function exchangeCodeForTokens(code: string, appId: string): Promise<GoogleTokenResponse | null> {
  try {
    const redirectUri = `${BASE_URL}/api/oauth/google/${appId}/callback`;
    
    const response = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: GOOGLE_CLIENT_ID,
        client_secret: GOOGLE_CLIENT_SECRET,
        code,
        grant_type: 'authorization_code',
        redirect_uri: redirectUri,
      }),
    });

    if (!response.ok) {
      console.error('Token exchange failed:', await response.text());
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error exchanging code for tokens:', error);
    return null;
  }
}

async function getGoogleUserInfo(accessToken: string): Promise<GoogleUserInfo | null> {
  try {
    const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      console.error('Failed to get user info:', await response.text());
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting user info:', error);
    return null;
  }
}

// Helper functions
function generateWalletAddress(): string {
  return '0x' + Array.from({ length: 40 }, () => 
    Math.floor(Math.random() * 16).toString(16)
  ).join('');
}

function generatePublicKey(): string {
  return '0x04' + Array.from({ length: 128 }, () => 
    Math.floor(Math.random() * 16).toString(16)
  ).join('');
}

// Simple in-memory store for auth results (use Redis/database in production)
const authResultStore = new Map<string, any>();

function storeAuthResult(appId: string, result: any): void {
  authResultStore.set(appId, result);
  
  // Clean up after 5 minutes
  setTimeout(() => {
    authResultStore.delete(appId);
  }, 5 * 60 * 1000);
}

// Export for frontend to retrieve auth result
export function getAuthResult(appId: string): any {
  return authResultStore.get(appId);
}
