import { NextRequest, NextResponse } from 'next/server';

// Google OAuth configuration
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID || 'your-google-client-id';
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || 'your-google-client-secret';
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

export async function GET(
  request: NextRequest,
  { params }: { params: { appId: string } }
) {
  try {
    const { appId } = params;
    
    // Validate appId
    if (!appId || typeof appId !== 'string') {
      return NextResponse.json({
        success: false,
        message: 'Invalid app ID',
      }, { status: 400 });
    }

    // Check if Google OAuth is configured
    if (!GOOGLE_CLIENT_ID || GOOGLE_CLIENT_ID === 'your-google-client-id') {
      console.warn('Google OAuth not configured. Please set GOOGLE_CLIENT_ID in environment variables.');
      return NextResponse.json({
        success: false,
        message: 'Google OAuth not configured',
      }, { status: 500 });
    }

    // Build Google OAuth URL
    const redirectUri = `${BASE_URL}/api/oauth/google/${appId}/callback`;
    const scope = 'openid email profile';
    const state = generateState(appId); // Generate state for CSRF protection
    
    const googleAuthUrl = new URL('https://accounts.google.com/o/oauth2/v2/auth');
    googleAuthUrl.searchParams.set('client_id', GOOGLE_CLIENT_ID);
    googleAuthUrl.searchParams.set('redirect_uri', redirectUri);
    googleAuthUrl.searchParams.set('response_type', 'code');
    googleAuthUrl.searchParams.set('scope', scope);
    googleAuthUrl.searchParams.set('state', state);
    googleAuthUrl.searchParams.set('access_type', 'offline');
    googleAuthUrl.searchParams.set('prompt', 'consent');

    // Store state in a temporary store (in production, use Redis or database)
    // For now, we'll use a simple in-memory store
    storeState(state, appId);

    // Redirect to Google OAuth consent screen
    return NextResponse.redirect(googleAuthUrl.toString(), { status: 302 });

  } catch (error) {
    console.error('Error initiating Google OAuth:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
    }, { status: 500 });
  }
}

// Helper function to generate state for CSRF protection
function generateState(appId: string): string {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2);
  return `${appId}_${timestamp}_${random}`;
}

// Simple in-memory store for state (use Redis/database in production)
const stateStore = new Map<string, { appId: string; timestamp: number }>();

function storeState(state: string, appId: string): void {
  stateStore.set(state, {
    appId,
    timestamp: Date.now(),
  });
  
  // Clean up expired states (older than 10 minutes)
  const tenMinutesAgo = Date.now() - (10 * 60 * 1000);
  for (const [key, value] of stateStore.entries()) {
    if (value.timestamp < tenMinutesAgo) {
      stateStore.delete(key);
    }
  }
}

// Export for use in callback
export function getStoredState(state: string): { appId: string; timestamp: number } | undefined {
  return stateStore.get(state);
}

export function deleteStoredState(state: string): void {
  stateStore.delete(state);
}
