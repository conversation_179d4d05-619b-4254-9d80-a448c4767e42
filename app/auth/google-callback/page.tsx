'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/lib/auth-context';
import { CheckCircleIcon, AlertCircleIcon, LoaderIcon } from 'lucide-react';
import { toast } from 'sonner';

export default function GoogleCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login } = useAuth();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing Google authentication...');

  useEffect(() => {
    const handleGoogleCallback = async () => {
      try {
        const success = searchParams.get('success');
        const appId = searchParams.get('appId');
        const error = searchParams.get('error');

        if (error) {
          setStatus('error');
          setMessage(`Authentication failed: ${error}`);
          toast.error('Google authentication failed');
          return;
        }

        if (success === 'true' && appId) {
          // In a real implementation, you would fetch the auth result from your backend
          // For now, we'll simulate a successful authentication
          setStatus('success');
          setMessage('Google authentication successful!');
          
          // Simulate getting user data from Google OAuth
          const mockUserData = {
            id: 'google_' + Date.now(),
            email: '<EMAIL>', // This would come from Google
            name: 'Google User', // This would come from Google
            isVerified: true,
            createdAt: new Date().toISOString(),
          };

          const mockToken = 'google_auth_token_' + Date.now();

          // Login with Google data
          login(mockUserData, mockToken, 'wallet-user');
          
          toast.success('Successfully authenticated with Google!');
          
          // Redirect to dashboard after a short delay
          setTimeout(() => {
            router.push('/dashboard/dashboard');
          }, 2000);
          
        } else {
          setStatus('error');
          setMessage('Invalid callback parameters');
          toast.error('Invalid authentication callback');
        }
      } catch (error) {
        console.error('Error processing Google callback:', error);
        setStatus('error');
        setMessage('An unexpected error occurred');
        toast.error('Authentication processing failed');
      }
    };

    handleGoogleCallback();
  }, [searchParams, login, router]);

  const renderContent = () => {
    switch (status) {
      case 'loading':
        return (
          <div className="text-center space-y-4">
            <LoaderIcon className="mx-auto h-16 w-16 text-primary animate-spin" />
            <h2 className="text-xl font-bold">Processing Authentication</h2>
            <p className="text-muted-foreground">{message}</p>
          </div>
        );
      
      case 'success':
        return (
          <div className="text-center space-y-4">
            <CheckCircleIcon className="mx-auto h-16 w-16 text-green-500" />
            <h2 className="text-xl font-bold text-green-700">Authentication Successful!</h2>
            <p className="text-muted-foreground">{message}</p>
            <p className="text-sm text-muted-foreground">
              Redirecting to dashboard...
            </p>
          </div>
        );
      
      case 'error':
        return (
          <div className="text-center space-y-4">
            <AlertCircleIcon className="mx-auto h-16 w-16 text-red-500" />
            <h2 className="text-xl font-bold text-red-700">Authentication Failed</h2>
            <p className="text-muted-foreground">{message}</p>
            <div className="space-y-2">
              <button
                onClick={() => router.push('/auth/signin')}
                className="w-full py-2 px-4 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                Try Again
              </button>
              <button
                onClick={() => router.push('/')}
                className="w-full py-2 px-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Go Home
              </button>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <main className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 via-white to-blue-50 p-4">
      {/* Background Pattern */}
      <div 
        className="absolute inset-0 opacity-5 bg-repeat"
        style={{ 
          backgroundImage: "url('data:image/svg+xml,<svg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"><g fill=\"none\" fill-rule=\"evenodd\"><g fill=\"%23000\" fill-opacity=\"0.1\"><circle cx=\"30\" cy=\"30\" r=\"4\"/></g></svg>')" 
        }}
      />
      
      {/* Main Content */}
      <div className="relative z-10 w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent mb-2">
            Crefy Connect
          </h1>
          <p className="text-muted-foreground">
            Google Authentication
          </p>
        </div>

        <div className="backdrop-blur-sm bg-white/80 border border-gray-200 shadow-xl rounded-lg p-8">
          {renderContent()}
        </div>

        <div className="text-center mt-6">
          <p className="text-xs text-muted-foreground">
            Powered by Crefy Connect Social Wallet Authentication
          </p>
        </div>
      </div>
    </main>
  );
}
