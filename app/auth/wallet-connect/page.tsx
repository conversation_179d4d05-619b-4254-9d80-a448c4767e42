'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { SocialWalletAuth } from '@/components/auth/social-wallet-auth';
import { WalletData } from '@/lib/api';
import { useAuth } from '@/lib/auth-context';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircleIcon, SettingsIcon } from 'lucide-react';

export default function WalletConnectPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login } = useAuth();
  const [appId, setAppId] = useState<string>('');
  const [showSetupPrompt, setShowSetupPrompt] = useState(false);

  useEffect(() => {
    // Get appId from URL params
    const urlAppId = searchParams.get('appId');
    if (urlAppId && urlAppId !== 'demo_app_id') {
      setAppId(urlAppId);
    } else {
      // Show setup prompt if no valid app ID is provided
      setShowSetupPrompt(true);
    }
  }, [searchParams]);

  const handleWalletSuccess = (walletData: WalletData, token: string) => {
    try {
      // Parse user data from wallet
      const userData = JSON.parse(walletData.userData);
      
      // Create user object for auth context
      const user = {
        id: walletData.walletAddress,
        email: userData.email,
        name: userData.name || '',
        isVerified: true,
        createdAt: new Date().toISOString(),
      };

      // Login with wallet data
      login(user, token, 'wallet-user');
      
      toast.success('Successfully connected to your social wallet!');
      
      // Redirect to dashboard
      setTimeout(() => {
        router.push('/dashboard/dashboard');
      }, 1000);
      
    } catch (error) {
      console.error('Error processing wallet data:', error);
      toast.error('Error processing wallet connection');
    }
  };

  const handleWalletError = (error: string) => {
    console.error('Wallet authentication error:', error);
    toast.error('Failed to connect wallet: ' + error);
  };

  if (showSetupPrompt) {
    return (
      <main className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 via-white to-blue-50 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertCircleIcon className="mx-auto h-12 w-12 text-orange-500 mb-4" />
            <CardTitle>Setup Required</CardTitle>
            <CardDescription>
              You need to create an application first to use Crefy Connect OAuth
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground text-center">
              Before you can test wallet authentication, you need to:
            </p>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• Register a developer account</li>
              <li>• Create an application</li>
              <li>• Get your App ID</li>
            </ul>
            <Button
              onClick={() => router.push('/setup')}
              className="w-full"
            >
              <SettingsIcon className="mr-2 h-4 w-4" />
              Go to Setup
            </Button>
          </CardContent>
        </Card>
      </main>
    );
  }

  if (!appId) {
    return (
      <main className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 via-white to-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </main>
    );
  }

  return (
    <main className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 via-white to-blue-50 p-4">
      {/* Background Pattern */}
      <div 
        className="absolute inset-0 opacity-5 bg-repeat"
        style={{ 
          backgroundImage: "url('data:image/svg+xml,<svg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"><g fill=\"none\" fill-rule=\"evenodd\"><g fill=\"%23000\" fill-opacity=\"0.1\"><circle cx=\"30\" cy=\"30\" r=\"4\"/></g></svg>')" 
        }}
      />
      
      {/* Main Content */}
      <div className="relative z-10 w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent mb-2">
            Crefy Connect
          </h1>
          <p className="text-muted-foreground">
            Connect your social wallet to access Web3 features
          </p>
        </div>

        <SocialWalletAuth
          appId={appId}
          onSuccess={handleWalletSuccess}
          onError={handleWalletError}
          className="backdrop-blur-sm bg-white/80 border border-gray-200 shadow-xl"
        />

        <div className="text-center mt-6 space-y-2">
          <p className="text-sm text-muted-foreground">
            Need help?{' '}
            <a 
              href="/docs" 
              className="text-[#4A148C] hover:underline font-medium"
            >
              View Documentation
            </a>
          </p>
          <p className="text-xs text-muted-foreground">
            Powered by Crefy Connect Social Wallet Authentication
          </p>
        </div>
      </div>
    </main>
  );
}
