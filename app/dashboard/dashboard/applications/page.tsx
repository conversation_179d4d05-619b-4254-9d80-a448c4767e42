"use client"

import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Plus, Copy, Eye, EyeOff, Trash2, CheckCircle, AlertCircle, Edit, MoreVertical, Settings } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { DashboardSidebar } from "@/components/shared/dashboard/sidebar"
import { ProfileDropdown } from "@/components/shared/profile-dropdown"
import { useAuth } from "@/lib/auth-context"
import { apiService, Application, CreateApplicationRequest, VerifyAppRequest, VerifyAppResponse } from "@/lib/api"
import { toast } from "sonner"

export default function ApplicationsPage() {
  const { token } = useAuth()
  const [applications, setApplications] = useState<Application[]>([])
  const [loading, setLoading] = useState(true)
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [verifyDialogOpen, setVerifyDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [showSecrets, setShowSecrets] = useState<{ [key: string]: boolean }>({})
  const [editingApp, setEditingApp] = useState<Application | null>(null)
  
  // Create application form state
  const [newApp, setNewApp] = useState<CreateApplicationRequest>({
    name: "",
    description: "",
    redirectUrls: [""],
    iconUrl: ""
  })

  // Edit application form state
  const [editApp, setEditApp] = useState<CreateApplicationRequest>({
    name: "",
    description: "",
    redirectUrls: [""],
    iconUrl: ""
  })
  
  // Verify application form state
  const [verifyData, setVerifyData] = useState<VerifyAppRequest>({
    appId: "",
    apiKey: ""
  })
  const [verifyResult, setVerifyResult] = useState<{
    success: boolean;
    data?: VerifyAppResponse;
    error?: string;
  } | null>(null)

  const fetchApplications = useCallback(async () => {
    if (!token) return
    
    try {
      setLoading(true)
      const response = await apiService.getApplications(token)
      
      if (response.success && response.data) {
        setApplications(response.data)
      } else {
        toast.error(response.error || "Failed to fetch applications")
      }
    } catch (error) {
      toast.error("Failed to fetch applications")
      console.error('Error fetching applications:', error)
    } finally {
      setLoading(false)
    }
  }, [token])

  useEffect(() => {
    fetchApplications()
  }, [fetchApplications])

  const handleCreateApplication = async () => {
    if (!token) {
      toast.error("Authentication required")
      return
    }

    if (!newApp.name.trim()) {
      toast.error("Application name is required")
      return
    }

    const validRedirectUrls = newApp.redirectUrls.filter(url => url.trim())
    if (validRedirectUrls.length === 0) {
      toast.error("At least one redirect URL is required")
      return
    }

    try {
      const response = await apiService.createApplication({
        name: newApp.name.trim(),
        description: newApp.description?.trim() || undefined,
        redirectUrls: validRedirectUrls,
        iconUrl: newApp.iconUrl?.trim() || undefined
      }, token)

      if (response.success && response.data) {
        toast.success("Application created successfully")
        setApplications([...applications, response.data])
        setCreateDialogOpen(false)
        setNewApp({ name: "", description: "", redirectUrls: [""], iconUrl: "" })
      } else {
        toast.error(response.error || "Failed to create application")
      }
    } catch (error) {
      toast.error("Failed to create application")
    }
  }

  const handleVerifyApplication = async () => {
    if (!token) {
      toast.error("Authentication required")
      return
    }

    if (!verifyData.appId.trim() || !verifyData.apiKey.trim()) {
      toast.error("Both App ID and API Key are required")
      return
    }

    try {
      const response = await apiService.verifyApplication(verifyData)
      
      setVerifyResult(response)
      if (response.success && response.data?.isValid) {
        toast.success("Application credentials are valid")
      } else {
        toast.error("Application credentials are invalid")
      }
    } catch (error) {
      toast.error("Failed to verify application")
      setVerifyResult({ success: false, data: undefined })
      console.error('Error verifying application:', error)
    }
  }

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text)
    toast.success(`${label} copied to clipboard`)
  }

  const copyApiKey = async (appId: string) => {
    const app = applications.find(a => a.appId === appId);
    if (app?.apiKey) {
      copyToClipboard(app.apiKey, "API Key");
      return;
    }

    // Fetch the individual app to get the API key
    if (!token) return;

    try {
      const response = await apiService.getApplication(appId, token);
      if (response.success && response.data?.apiKey) {
        // Update the application in the list with the API key
        setApplications(prev => prev.map(app =>
          app.appId === appId ? { ...app, apiKey: response.data!.apiKey } : app
        ));
        copyToClipboard(response.data.apiKey, "API Key");
      } else {
        toast.error("Failed to get API key");
      }
    } catch {
      toast.error("Failed to fetch API key");
    }
  }

  const toggleSecretVisibility = async (appId: string) => {
    if (!showSecrets[appId] && !applications.find(app => app.appId === appId)?.apiKey) {
      // Fetch the individual app to get the API key
      if (!token) return;

      try {
        const response = await apiService.getApplication(appId, token);
        if (response.success && response.data) {
          // Update the application in the list with the API key
          setApplications(prev => prev.map(app =>
            app.appId === appId ? { ...app, apiKey: response.data!.apiKey } : app
          ));
        }
      } catch (error) {
        toast.error("Failed to fetch application details");
        return;
      }
    }

    setShowSecrets(prev => ({
      ...prev,
      [appId]: !prev[appId]
    }))
  }

  const addRedirectUrlField = () => {
    setNewApp(prev => ({
      ...prev,
      redirectUrls: [...prev.redirectUrls, ""]
    }))
  }

  const updateRedirectUrl = (index: number, value: string) => {
    setNewApp(prev => ({
      ...prev,
      redirectUrls: prev.redirectUrls.map((url, i) => i === index ? value : url)
    }))
  }

  const removeRedirectUrl = (index: number) => {
    if (newApp.redirectUrls.length > 1) {
      setNewApp(prev => ({
        ...prev,
        redirectUrls: prev.redirectUrls.filter((_, i) => i !== index)
      }))
    }
  }

  // Edit form helper functions
  const addEditRedirectUrlField = () => {
    setEditApp(prev => ({
      ...prev,
      redirectUrls: [...prev.redirectUrls, ""]
    }))
  }

  const updateEditRedirectUrl = (index: number, value: string) => {
    setEditApp(prev => ({
      ...prev,
      redirectUrls: prev.redirectUrls.map((url, i) => i === index ? value : url)
    }))
  }

  const removeEditRedirectUrl = (index: number) => {
    if (editApp.redirectUrls.length > 1) {
      setEditApp(prev => ({
        ...prev,
        redirectUrls: prev.redirectUrls.filter((_, i) => i !== index)
      }))
    }
  }

  const handleEditApplication = (app: Application) => {
    setEditingApp(app)
    setEditApp({
      name: app.name,
      description: app.description || "",
      redirectUrls: [...app.redirectUrls],
      iconUrl: app.iconUrl || ""
    })
    setEditDialogOpen(true)
  }

  const handleUpdateApplication = async () => {
    if (!token || !editingApp) {
      toast.error("Authentication required")
      return
    }

    if (!editApp.name.trim()) {
      toast.error("Application name is required")
      return
    }

    const validRedirectUrls = editApp.redirectUrls.filter(url => url.trim())
    if (validRedirectUrls.length === 0) {
      toast.error("At least one redirect URL is required")
      return
    }

    try {
      const response = await apiService.updateApplication(editingApp.appId, {
        name: editApp.name.trim(),
        description: editApp.description?.trim() || undefined,
        redirectUrls: validRedirectUrls,
        iconUrl: editApp.iconUrl?.trim() || undefined
      }, token)

      if (response.success && response.data) {
        toast.success("Application updated successfully")
        setApplications(applications.map(app =>
          app.appId === editingApp.appId ? { ...app, ...response.data } : app
        ))
        setEditDialogOpen(false)
        setEditingApp(null)
        setEditApp({ name: "", description: "", redirectUrls: [""], iconUrl: "" })
      } else {
        toast.error(response.error || "Failed to update application")
      }
    } catch {
      toast.error("Failed to update application")
    }
  }

  const handleDeleteApplication = async (appId: string) => {
    if (!token) {
      toast.error("Authentication required")
      return
    }

    if (!confirm("Are you sure you want to delete this application? This action cannot be undone.")) {
      return
    }

    try {
      const response = await apiService.deleteApplication(appId, token)
      
      if (response.success) {
        toast.success("Application deleted successfully")
        setApplications(applications.filter(app => app.appId !== appId))
      } else {
        toast.error(response.error || "Failed to delete application")
      }
    } catch (error) {
      toast.error("Failed to delete application")
      console.error('Error deleting application:', error)
    }
  }

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute top-[10%] left-[5%] w-64 h-64 bg-[#7B1FA2]/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute top-[60%] right-[10%] w-80 h-80 bg-[#4A148C]/5 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-[20%] left-[20%] w-48 h-48 bg-[#7B1FA2]/5 rounded-full blur-3xl animate-bounce-slow"></div>

      <div className="flex min-h-screen relative z-10">
        <DashboardSidebar />
        
        <main className="flex-1 p-6 lg:p-8">
          <header className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                Applications Management
              </h1>
              <p className="text-gray-600 mt-2">
                Create and manage your OAuth applications and API credentials
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex gap-2">
                <Dialog open={verifyDialogOpen} onOpenChange={setVerifyDialogOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" className="bg-white border-purple">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Verify Credentials
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Verify Application Credentials</DialogTitle>
                      <DialogDescription>
                        Verify if your application credentials are valid and active
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="verify-app-id">App ID</Label>
                        <Input
                          id="verify-app-id"
                          placeholder="app_1234567890_abcdef"
                          value={verifyData.appId}
                          onChange={(e) => setVerifyData(prev => ({ ...prev, appId: e.target.value }))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="verify-api-key">API Key</Label>
                        <Input
                          id="verify-api-key"
                          type="password"
                          placeholder="ak_1234567890_abcdefghijk"
                          value={verifyData.apiKey}
                          onChange={(e) => setVerifyData(prev => ({ ...prev, apiKey: e.target.value }))}
                        />
                      </div>
                      {verifyResult && (
                        <div className={`p-4 rounded-lg border ${verifyResult.success && verifyResult.data?.isValid ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                          <div className="flex items-center gap-2">
                            {verifyResult.success && verifyResult.data?.isValid ? (
                              <CheckCircle className="w-5 h-5 text-green-600" />
                            ) : (
                              <AlertCircle className="w-5 h-5 text-red-600" />
                            )}
                            <span className={`font-medium ${verifyResult.success && verifyResult.data?.isValid ? 'text-green-800' : 'text-red-800'}`}>
                              {verifyResult.success && verifyResult.data?.isValid ? 'Valid Credentials' : 'Invalid Credentials'}
                            </span>
                          </div>
                          {verifyResult.success && verifyResult.data?.isValid && verifyResult.data && (
                            <div className="mt-2 text-sm text-gray-600">
                              <p><strong>App ID:</strong> {verifyResult.data.appId}</p>
                              <p><strong>Developer ID:</strong> {verifyResult.data.developerId}</p>
                              <p><strong>Redirect URLs:</strong> {verifyResult.data.redirectUrls?.join(', ')}</p>
                            </div>
                          )}
                        </div>
                      )}
                      <Button onClick={handleVerifyApplication} className="w-full">
                        Verify Credentials
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
                
                <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
                  <DialogTrigger asChild>
                    <Button className="bg-[#4A148C] text-white hover:bg-opacity-90 transition-all duration-300 shadow-md hover:shadow-lg">
                      <Plus className="w-4 h-4 mr-2" />
                      New Application
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto bg-white">
                    <DialogHeader>
                      <DialogTitle>Create New Application</DialogTitle>
                      <DialogDescription>
                        Create a new OAuth application to get client credentials
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-6">
                      <div>
                        <Label htmlFor="app-name">Application Name *</Label>
                        <Input
                          id="app-name"
                          placeholder="My Awesome App"
                          value={newApp.name}
                          onChange={(e) => setNewApp(prev => ({ ...prev, name: e.target.value }))}
                        />
                      </div>

                      <div>
                        <Label htmlFor="app-description">Description</Label>
                        <Input
                          id="app-description"
                          placeholder="A description of my app"
                          value={newApp.description || ""}
                          onChange={(e) => setNewApp(prev => ({ ...prev, description: e.target.value }))}
                        />
                      </div>

                      <div>
                        <Label>Redirect URLs *</Label>
                        <p className="text-sm text-gray-600 mb-2">
                          URLs where users will be redirected after authentication
                        </p>
                        {newApp.redirectUrls.map((url, index) => (
                          <div key={index} className="flex gap-2 mt-2">
                            <Input
                              placeholder="https://myapp.com/callback"
                              value={url}
                              onChange={(e) => updateRedirectUrl(index, e.target.value)}
                            />
                            {newApp.redirectUrls.length > 1 && (
                              <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                onClick={() => removeRedirectUrl(index)}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            )}
                          </div>
                        ))}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={addRedirectUrlField}
                          className="mt-2"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Add Redirect URL
                        </Button>
                      </div>

                      <div>
                        <Label htmlFor="app-icon">Icon URL</Label>
                        <Input
                          id="app-icon"
                          placeholder="https://myapp.com/icon.png"
                          value={newApp.iconUrl || ""}
                          onChange={(e) => setNewApp(prev => ({ ...prev, iconUrl: e.target.value }))}
                        />
                      </div>

                      <Button onClick={handleCreateApplication} className="w-full">
                        Create Application
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>

                {/* Edit Application Dialog */}
                <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
                  <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle>Edit Application</DialogTitle>
                      <DialogDescription>
                        Update your application settings and redirect URLs
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-6">
                      <div>
                        <Label htmlFor="edit-name">Application Name</Label>
                        <Input
                          id="edit-name"
                          value={editApp.name}
                          onChange={(e) => setEditApp({ ...editApp, name: e.target.value })}
                          placeholder="My Awesome App"
                          className="mt-1"
                        />
                      </div>

                      <div>
                        <Label htmlFor="edit-description">Description</Label>
                        <Input
                          id="edit-description"
                          value={editApp.description || ""}
                          onChange={(e) => setEditApp({ ...editApp, description: e.target.value })}
                          placeholder="A description of my app"
                          className="mt-1"
                        />
                      </div>

                      <div>
                        <Label>Redirect URLs</Label>
                        <div className="space-y-2 mt-2">
                          {editApp.redirectUrls.map((url, index) => (
                            <div key={index} className="flex gap-2">
                              <Input
                                value={url}
                                onChange={(e) => updateEditRedirectUrl(index, e.target.value)}
                                placeholder="https://myapp.com/callback"
                                className="flex-1"
                              />
                              {editApp.redirectUrls.length > 1 && (
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => removeEditRedirectUrl(index)}
                                  className="px-3"
                                >
                                  <X className="w-4 h-4" />
                                </Button>
                              )}
                            </div>
                          ))}
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={addEditRedirectUrlField}
                          className="mt-2"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Add Redirect URL
                        </Button>
                      </div>

                      <div>
                        <Label htmlFor="edit-icon">Icon URL</Label>
                        <Input
                          id="edit-icon"
                          value={editApp.iconUrl || ""}
                          onChange={(e) => setEditApp({ ...editApp, iconUrl: e.target.value })}
                          placeholder="https://myapp.com/icon.png"
                          className="mt-1"
                        />
                      </div>

                      <Button onClick={handleUpdateApplication} className="w-full">
                        Update Application
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
              <ProfileDropdown />
            </div>
          </header>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Applications</p>
                    <p className="text-2xl font-bold text-gray-900">{applications.length}</p>
                  </div>
                  <Settings className="h-8 w-8 text-[#4A148C]" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Redirect URLs</p>
                    <p className="text-2xl font-bold text-green-600">
                      {applications.reduce((acc, app) => acc + app.redirectUrls.length, 0)}
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Recent Apps</p>
                    <p className="text-2xl font-bold text-[#4A148C]">
                      {applications.filter(app => {
                        const createdDate = new Date(app.createdAt)
                        const weekAgo = new Date()
                        weekAgo.setDate(weekAgo.getDate() - 7)
                        return createdDate > weekAgo
                      }).length}
                    </p>
                  </div>
                  <Badge className="h-8 w-8 rounded-full bg-[#7B1FA2] text-white flex items-center justify-center">
                    {applications.filter(app => {
                      const createdDate = new Date(app.createdAt)
                      const weekAgo = new Date()
                      weekAgo.setDate(weekAgo.getDate() - 7)
                      return createdDate > weekAgo
                    }).length}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {loading ? (
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#4A148C]"></div>
            </div>
          ) : applications.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <div className="text-center">
                  <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No applications yet</h3>
                  <p className="text-gray-600 mb-4">Create your first OAuth application to get started</p>
                  <Button onClick={() => setCreateDialogOpen(true)} className="bg-[#4A148C] text-white hover:bg-opacity-90">
                    <Plus className="w-4 h-4 mr-2" />
                    Create Application
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-6">
              {applications.map((app) => (
                <Card key={app.appId} className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-lg hover:shadow-2xl transform transition-all duration-300 hover:-translate-y-1">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-xl">{app.name}</CardTitle>
                        <CardDescription>
                          Created on {new Date(app.createdAt).toLocaleDateString()}
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="default" className="bg-green-100 text-green-800">
                          Active
                        </Badge>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditApplication(app)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              className="text-red-600 focus:text-red-600"
                              onClick={() => handleDeleteApplication(app.appId)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium">App ID</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <code className="flex-1 p-2 bg-gray-100 rounded text-sm font-mono">
                          {app.appId}
                        </code>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(app.appId, "App ID")}
                          className="border-[#4A148C] text-[#4A148C] hover:bg-[#7B1FA2] hover:text-white"
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    {app.description && (
                      <div>
                        <Label className="text-sm font-medium">Description</Label>
                        <p className="text-sm text-gray-600 mt-1">{app.description}</p>
                      </div>
                    )}

                    <div>
                      <Label className="text-sm font-medium">Redirect URLs</Label>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {app.redirectUrls.map((url, index) => (
                          <Badge key={index} variant="outline" className="border-[#7B1FA2] text-[#4A148C] font-mono text-xs">
                            {url}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {app.iconUrl && (
                      <div>
                        <Label className="text-sm font-medium">Icon URL</Label>
                        <div className="flex items-center gap-2 mt-1">
                          <code className="flex-1 p-2 bg-gray-100 rounded text-sm font-mono">
                            {app.iconUrl}
                          </code>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copyToClipboard(app.iconUrl, "Icon URL")}
                            className="border-[#4A148C] text-[#4A148C] hover:bg-[#7B1FA2] hover:text-white"
                          >
                            <Copy className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </main>
      </div>
    </div>
  )
}