"use client"

import React, { useEffect, useState } from "react"
import { useAuth } from "@/lib/auth-context"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useRouter } from "next/navigation"
import { apiService } from "@/lib/api"
import { DashboardSidebar } from "@/components/shared/dashboard/sidebar"
import { ProfileDropdown } from "@/components/shared/profile-dropdown"

export default function ProfilePage() {
  const { user, token, refreshProfile } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [editMode, setEditMode] = useState(false)
  const [formData, setFormData] = useState({
    email: user?.email || "",
    role: user?.role || "developer"
  })
  
  // Redirect if not authenticated
  useEffect(() => {
    if (!user || !token) {
      router.push("/auth/signin")
    } else {
      // Refresh user profile data when page loads
      refreshProfile()
    }
  }, [user, token, router, refreshProfile])
  
  // Get initials for avatar
  const getInitials = (email: string) => {
    return email.split('@')[0].substring(0, 1).toUpperCase()
  }
  
  // Format date to be more readable
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A"
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }
  
  // Handle form field changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }
  
  // Toggle edit mode
  const toggleEditMode = () => {
    setEditMode(!editMode)
    
    // Reset form data when entering edit mode
    if (!editMode && user) {
      setFormData({
        email: user.email,
        role: user.role
      })
    }
  }
  
  // Save profile changes
  const saveChanges = async () => {
    setIsLoading(true)
    // In a real implementation, you'd call an API to update user details
    // Since we don't have that endpoint yet, we'll just simulate success
    setTimeout(() => {
      refreshProfile()
      setEditMode(false)
      setIsLoading(false)
    }, 1000)
  }
  
  return (
    <div className="flex min-h-screen bg-slate-50">
      <DashboardSidebar />
      
      <main className="flex-1 p-6 md:p-10">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-[#4B0082]">Profile</h1>
          <ProfileDropdown />
        </div>
        
        <div className="max-w-4xl mx-auto">
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* User overview card */}
        <Card className="p-6 border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <div className="flex flex-col items-center text-center">
            <Avatar className="h-24 w-24 border-4 border-[#B497D6]/30">
              <AvatarFallback className="bg-gradient-to-br from-[#4B0082] to-[#B497D6] text-white font-bold text-3xl">
                {user?.email ? getInitials(user.email) : "U"}
              </AvatarFallback>
            </Avatar>
            
            <h2 className="mt-4 text-xl font-semibold">
              {user?.email?.split('@')[0] || "User"}
            </h2>
            <p className="text-sm text-gray-500 mt-1">{user?.email}</p>
            
            <div className="mt-3 px-3 py-1 bg-[#B497D6]/10 rounded-full text-[#4B0082] font-medium">
              {user?.role || "developer"}
            </div>
            
            <div className="mt-6 w-full">
              <div className="flex justify-between text-sm mb-2">
                <span className="text-gray-500">Account ID:</span>
                <span className="font-medium">{user?.id || "N/A"}</span>
              </div>
              <div className="flex justify-between text-sm mb-2">
                <span className="text-gray-500">Created:</span>
                <span className="font-medium">{formatDate(user?.createdAt)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Updated:</span>
                <span className="font-medium">{formatDate(user?.updatedAt)}</span>
              </div>
            </div>
          </div>
        </Card>
        
        {/* User details card */}
        <Card className="p-6 col-span-1 md:col-span-2 border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-[#4B0082]">Account Details</h2>
            <Button 
              variant="ghost" 
              onClick={toggleEditMode}
              className="text-[#4B0082] hover:bg-[#B497D6]/10"
            >
              {editMode ? "Cancel" : "Edit"}
            </Button>
          </div>
          
          <div className="space-y-6">
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                disabled={!editMode}
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="role">Role</Label>
              <Input
                id="role"
                name="role"
                value={formData.role}
                onChange={handleInputChange}
                disabled={!editMode}
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="verification">Verification Status</Label>
              <div className="mt-1 flex items-center">
                <div className={`h-3 w-3 rounded-full mr-2 ${user?.isVerified ? "bg-green-500" : "bg-amber-500"}`}></div>
                <span>{user?.isVerified ? "Verified" : "Pending Verification"}</span>
              </div>
            </div>

            {editMode && (
              <Button
                onClick={saveChanges}
                disabled={isLoading}
                className="w-full bg-[#4B0082] hover:bg-[#4B0082]/90 text-white mt-4"
              >
                {isLoading ? "Saving..." : "Save Changes"}
              </Button>
            )}
          </div>
        </Card>
        
        {/* Activity and usage stats card */}
        <Card className="p-6 col-span-1 md:col-span-3 border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <h2 className="text-xl font-semibold text-[#4B0082] mb-6">Account Activity</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-[#B497D6]/10 rounded-xl">
              <h3 className="text-sm font-medium text-gray-500">Last Login</h3>
              <p className="text-lg font-semibold mt-1">Today, 2:34 PM</p>
            </div>
            
            <div className="p-4 bg-[#B497D6]/10 rounded-xl">
              <h3 className="text-sm font-medium text-gray-500">API Calls (This Month)</h3>
              <p className="text-lg font-semibold mt-1">243</p>
            </div>
            
            <div className="p-4 bg-[#B497D6]/10 rounded-xl">
              <h3 className="text-sm font-medium text-gray-500">Applications</h3>
              <p className="text-lg font-semibold mt-1">2</p>
            </div>
          </div>
          
          <div className="mt-6">
            <Button 
              variant="outline" 
              onClick={() => router.push("/dashboard/dashboard/api-keys")}
              className="border-[#B497D6]/30 hover:border-[#4B0082] hover:bg-[#B497D6]/10"
            >
              Manage API Keys
            </Button>
          </div>
        </Card>
      </div>
        </div>
      </main>
    </div>
  )
}
