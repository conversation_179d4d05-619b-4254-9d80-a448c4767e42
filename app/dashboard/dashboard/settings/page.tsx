"use client"

import React, { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { useTheme } from "next-themes"
import { useAuth } from "@/lib/auth-context"
import { apiService } from "@/lib/api"
import { useRouter } from "next/navigation"
import { DashboardSidebar } from "@/components/shared/dashboard/sidebar"
import { ProfileDropdown } from "@/components/shared/profile-dropdown"

export default function SettingsPage() {
  const { theme, setTheme } = useTheme()
  const { user, token, refreshProfile } = useAuth()
  const router = useRouter()
  const [isSaving, setIsSaving] = useState(false)
  const [successMessage, setSuccessMessage] = useState("")
  const [errorMessage, setErrorMessage] = useState("")
  
  // Password change state
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  })
  
  // Account settings state
  const [accountSettings, setAccountSettings] = useState({
    sessionTimeout: "30",
    twoFactorAuth: false,
    language: "english"
  })
  
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      browser: false,
      web3: true,
      apiUsageAlerts: true,
      securityAlerts: true,
      marketingEmails: false
    },
    autoconnect: true,
    theme: theme || "system"
  })

  // Ensure user is authenticated
  useEffect(() => {
    if (!user || !token) {
      router.push("/auth/signin")
    }
  }, [user, token, router])

  useEffect(() => {
    setSettings(prev => ({ ...prev, theme: theme || "system" }))
  }, [theme])

  // Handle password field changes
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setPasswordData(prev => ({ ...prev, [name]: value }))
  }
  
  // Change password
  const handleChangePassword = async () => {
    setIsSaving(true)
    setSuccessMessage("")
    setErrorMessage("")
    
    // Validate passwords
    if (!passwordData.currentPassword) {
      setErrorMessage("Current password is required")
      setIsSaving(false)
      return
    }
    
    if (passwordData.newPassword.length < 8) {
      setErrorMessage("New password must be at least 8 characters")
      setIsSaving(false)
      return
    }
    
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setErrorMessage("New passwords don't match")
      setIsSaving(false)
      return
    }
    
    try {
      // In a real implementation, you would call an API endpoint to change the password
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Reset fields after successful password change
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: ""
      })
      
      setSuccessMessage("Password changed successfully")
    } catch (error) {
      setErrorMessage("Failed to change password")
    } finally {
      setIsSaving(false)
    }
  }

  const handleSave = () => {
    setIsSaving(true)
    setSuccessMessage("")
    setErrorMessage("")
    
    // Update theme
    if (settings.theme !== theme) {
      setTheme(settings.theme)
    }
    
    // Simulate API call
    setTimeout(() => {
      setSuccessMessage("Settings saved successfully!")
      setIsSaving(false)
    }, 1000)
  }

  return (
    <div className="flex min-h-screen bg-slate-50">
      <DashboardSidebar />
      
      <main className="flex-1 p-6 md:p-10">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-[#4B0082]">Settings</h1>
          <ProfileDropdown />
        </div>
        
        <div className="max-w-4xl mx-auto">
          {/* Success message */}
      {successMessage && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg text-green-800">
          {successMessage}
        </div>
      )}
      
      {/* Error message */}
      {errorMessage && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-800">
          {errorMessage}
        </div>
      )}

      <div className="space-y-8">
        {/* Notification Settings */}
        <Card className="p-6 border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <h2 className="text-xl font-semibold text-[#4B0082] mb-6">Notification Preferences</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="email-notifications">Email Notifications</Label>
                <p className="text-sm text-gray-500">Receive email notifications about your account activity</p>
              </div>
              <Switch
                id="email-notifications"
                checked={settings.notifications.email}
                onCheckedChange={(checked: boolean) => setSettings({
                  ...settings,
                  notifications: { ...settings.notifications, email: checked }
                })}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="browser-notifications">Browser Notifications</Label>
                <p className="text-sm text-gray-500">Receive notifications in your browser</p>
              </div>
              <Switch
                id="browser-notifications"
                checked={settings.notifications.browser}
                onCheckedChange={(checked: boolean) => setSettings({
                  ...settings,
                  notifications: { ...settings.notifications, browser: checked }
                })}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="web3-notifications">Web3 Events</Label>
                <p className="text-sm text-gray-500">Get notified about important Web3 events</p>
              </div>
              <Switch
                id="web3-notifications"
                checked={settings.notifications.web3}
                onCheckedChange={(checked: boolean) => setSettings({
                  ...settings,
                  notifications: { ...settings.notifications, web3: checked }
                })}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="api-usage-alerts">API Usage Alerts</Label>
                <p className="text-sm text-gray-500">Get notified when you approach your API usage limits</p>
              </div>
              <Switch
                id="api-usage-alerts"
                checked={settings.notifications.apiUsageAlerts}
                onCheckedChange={(checked: boolean) => setSettings({
                  ...settings,
                  notifications: { ...settings.notifications, apiUsageAlerts: checked }
                })}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="security-alerts">Security Alerts</Label>
                <p className="text-sm text-gray-500">Receive notifications about security events</p>
              </div>
              <Switch
                id="security-alerts"
                checked={settings.notifications.securityAlerts}
                onCheckedChange={(checked: boolean) => setSettings({
                  ...settings,
                  notifications: { ...settings.notifications, securityAlerts: checked }
                })}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="marketing-emails">Marketing Emails</Label>
                <p className="text-sm text-gray-500">Receive updates about new features and offers</p>
              </div>
              <Switch
                id="marketing-emails"
                checked={settings.notifications.marketingEmails}
                onCheckedChange={(checked: boolean) => setSettings({
                  ...settings,
                  notifications: { ...settings.notifications, marketingEmails: checked }
                })}
              />
            </div>
          </div>
        </Card>

        {/* General Settings */}
        <Card className="p-6 border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <h2 className="text-xl font-semibold text-[#4B0082] mb-6">General Settings</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="theme">Theme Preference</Label>
              <select
                id="theme"
                value={settings.theme}
                onChange={(e) => setSettings({ ...settings, theme: e.target.value })}
                className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm"
              >
                <option value="system">System Default</option>
                <option value="light">Light</option>
                <option value="dark">Dark</option>
              </select>
            </div>
            
            <div>
              <Label htmlFor="autoconnect">Auto-connect Wallet</Label>
              <div className="mt-1 flex items-center">
                <Switch
                  id="autoconnect"
                  checked={settings.autoconnect}
                  onCheckedChange={(checked: boolean) => setSettings({ ...settings, autoconnect: checked })}
                  className="mr-2"
                />
                <span className="text-sm text-gray-500">
                  {settings.autoconnect ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>
            
            <div>
              <Label htmlFor="sessionTimeout">Session Timeout</Label>
              <select
                id="sessionTimeout"
                value={accountSettings.sessionTimeout}
                onChange={(e) => setAccountSettings({ ...accountSettings, sessionTimeout: e.target.value })}
                className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm"
              >
                <option value="15">15 minutes</option>
                <option value="30">30 minutes</option>
                <option value="60">60 minutes</option>
                <option value="120">120 minutes</option>
              </select>
            </div>
            
            <div>
              <Label htmlFor="language">Language</Label>
              <select
                id="language"
                value={accountSettings.language}
                onChange={(e) => setAccountSettings({ ...accountSettings, language: e.target.value })}
                className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm"
              >
                <option value="english">English</option>
                <option value="spanish">Spanish</option>
                <option value="french">French</option>
                <option value="german">German</option>
              </select>
            </div>
          </div>
        </Card>
        
        {/* Security Settings */}
        <Card className="p-6 border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <h2 className="text-xl font-semibold text-[#4B0082] mb-6">Security Settings</h2>
          
          <div>
            <h3 className="text-lg font-medium mb-4">Change Password</h3>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="currentPassword">Current Password</Label>
                <Input
                  id="currentPassword"
                  name="currentPassword"
                  type="password"
                  value={passwordData.currentPassword}
                  onChange={handlePasswordChange}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="newPassword">New Password</Label>
                <Input
                  id="newPassword"
                  name="newPassword"
                  type="password"
                  value={passwordData.newPassword}
                  onChange={handlePasswordChange}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="confirmPassword">Confirm New Password</Label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  value={passwordData.confirmPassword}
                  onChange={handlePasswordChange}
                  className="mt-1"
                />
              </div>
              
              <Button
                onClick={handleChangePassword}
                disabled={isSaving}
                className="bg-[#4B0082] hover:bg-[#4B0082]/90 text-white"
              >
                {isSaving ? "Changing..." : "Change Password"}
              </Button>
            </div>
            
            <div className="mt-8 border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium mb-4">Two-Factor Authentication</h3>
              
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">
                    {accountSettings.twoFactorAuth 
                      ? "Two-factor authentication is currently enabled" 
                      : "Enable two-factor authentication for added security"}
                  </p>
                </div>
                <Button
                  variant={accountSettings.twoFactorAuth ? "outline" : "default"}
                  onClick={() => {
                    setAccountSettings({
                      ...accountSettings,
                      twoFactorAuth: !accountSettings.twoFactorAuth
                    })
                    setSuccessMessage(
                      accountSettings.twoFactorAuth 
                        ? "Two-factor authentication disabled" 
                        : "Two-factor authentication enabled"
                    )
                  }}
                  className={accountSettings.twoFactorAuth 
                    ? "border-red-500 text-red-500 hover:bg-red-50" 
                    : "bg-[#4B0082] hover:bg-[#4B0082]/90 text-white"}
                >
                  {accountSettings.twoFactorAuth ? "Disable 2FA" : "Enable 2FA"}
                </Button>
              </div>
            </div>
          </div>
        </Card>

        <div className="flex justify-end">
          <Button 
            onClick={handleSave} 
            disabled={isSaving} 
            className="bg-[#4B0082] hover:bg-[#4B0082]/90 text-white"
          >
            {isSaving ? "Saving..." : "Save All Changes"}
          </Button>
        </div>
      </div>
        </div>
      </main>
    </div>
  )
}