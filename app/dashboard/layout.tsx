"use client"
import { useRouter } from "next/navigation"
import { useAuth } from "@/lib/auth-context"
import { ToastProvider } from "@/lib/toast-context"
import { useEffect } from "react"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  
  useEffect(() => {
    // Only redirect if not loading and no user
    if (!isLoading && !user) {
      router.push('/auth/signin')
      return
    }
  }, [user, isLoading, router])
  
  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }
  
  // Don't render children if user is not authenticated
  if (!user) {
    return null
  }
  
  return (
      <ToastProvider>
        {children}
      </ToastProvider>
  )
}