'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { DeveloperSetup } from '@/components/setup/developer-setup';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRightIcon, SettingsIcon } from "lucide-react";

export default function SetupPage() {
  const router = useRouter();
  const [createdAppId, setCreatedAppId] = useState<string>('');

  const handleAppIdCreated = (appId: string) => {
    setCreatedAppId(appId);
  };

  const goToWalletConnect = () => {
    if (createdAppId) {
      router.push(`/auth/wallet-connect?appId=${createdAppId}`);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-2">
            <SettingsIcon className="h-8 w-8 text-primary" />
            <h1 className="text-3xl font-bold text-gray-900">
              Crefy Connect Setup
            </h1>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Set up your developer account and create an application to start using Crefy Connect OAuth integration.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 items-start">
          {/* Setup Component */}
          <div>
            <DeveloperSetup onAppIdCreated={handleAppIdCreated} />
          </div>

          {/* Instructions */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Setup Instructions</CardTitle>
                <CardDescription>
                  Follow these steps to get started with Crefy Connect
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <Badge variant="outline" className="mt-0.5">1</Badge>
                    <div>
                      <h4 className="font-medium">Register Developer Account</h4>
                      <p className="text-sm text-muted-foreground">
                        Create your developer account with the Crefy Connect API
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <Badge variant="outline" className="mt-0.5">2</Badge>
                    <div>
                      <h4 className="font-medium">Verify Email</h4>
                      <p className="text-sm text-muted-foreground">
                        Check your email for a verification code and enter it
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <Badge variant="outline" className="mt-0.5">3</Badge>
                    <div>
                      <h4 className="font-medium">Login</h4>
                      <p className="text-sm text-muted-foreground">
                        Login with your credentials to get an access token
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <Badge variant="outline" className="mt-0.5">4</Badge>
                    <div>
                      <h4 className="font-medium">Create Application</h4>
                      <p className="text-sm text-muted-foreground">
                        Create an application to get your App ID for OAuth
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {createdAppId && (
              <Card className="border-green-200 bg-green-50">
                <CardHeader>
                  <CardTitle className="text-green-800">Ready to Test!</CardTitle>
                  <CardDescription className="text-green-700">
                    Your application has been created successfully
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-green-800 mb-2">Your App ID:</p>
                    <Badge variant="secondary" className="font-mono text-green-800 bg-green-100">
                      {createdAppId}
                    </Badge>
                  </div>
                  
                  <Button 
                    onClick={goToWalletConnect}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    Test Wallet Connect
                    <ArrowRightIcon className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            )}

            <Card>
              <CardHeader>
                <CardTitle>What's Next?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  After completing the setup, you'll be able to:
                </p>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Use Social Wallet Authentication</li>
                  <li>• Integrate with email and Google OAuth</li>
                  <li>• Connect with Coinbase Wallet</li>
                  <li>• Manage user wallets and credentials</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          <p>
            Need help? Check the{' '}
            <a href="#" className="text-primary hover:underline">
              documentation
            </a>{' '}
            or{' '}
            <a href="#" className="text-primary hover:underline">
              contact support
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
