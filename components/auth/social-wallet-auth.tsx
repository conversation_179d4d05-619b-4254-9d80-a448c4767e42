'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Modal } from '@/components/shared/modal';
import { 
  WalletIcon, 
  MailIcon, 
  ShieldCheckIcon,
  ArrowRightIcon,
  RefreshCwIcon,
  CheckCircleIcon,
  AlertCircleIcon
} from 'lucide-react';
import { apiService, WalletData } from '@/lib/api';
import { toast } from 'sonner';

interface SocialWalletAuthProps {
  appId: string;
  onSuccess: (walletData: WalletData, token: string) => void;
  onError?: (error: string) => void;
  className?: string;
}

type AuthStep = 'select' | 'email-input' | 'email-verify' | 'google-auth' | 'success';

export function SocialWalletAuth({ 
  appId, 
  onSuccess, 
  onError,
  className = '' 
}: SocialWalletAuthProps) {
  const [step, setStep] = useState<AuthStep>('select');
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState('');
  const [walletExists, setWalletExists] = useState(false);
  const [cooldown, setCooldown] = useState(0);

  // Cooldown timer effect
  useEffect(() => {
    if (cooldown > 0) {
      const timer = setTimeout(() => setCooldown(cooldown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [cooldown]);

  const handleEmailLogin = async () => {
    if (!email || !email.includes('@')) {
      setStatus('Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    setStatus('Sending verification code...');

    try {
      const response = await apiService.socialWalletEmailLogin(appId, { email });
      
      if (response.success && response.data) {
        if (response.data.isActive && response.data.data) {
          // User is already authenticated
          onSuccess(response.data.data, response.data.token!);
          setStep('success');
        } else {
          // Need OTP verification
          setWalletExists(response.data.walletExists);
          setStep('email-verify');
          setStatus(response.data.message);
        }
      } else {
        setStatus(response.error || 'Failed to send verification code');
      }
    } catch (error) {
      setStatus('Network error. Please try again.');
      onError?.('Network error during email login');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOTPVerification = async () => {
    if (!otp || otp.length !== 6) {
      setStatus('Please enter a valid 6-digit code');
      return;
    }

    setIsLoading(true);
    setStatus('Verifying code...');

    try {
      const response = await apiService.socialWalletVerify(appId, { email, otp });
      
      if (response.success && response.data) {
        setStatus('Verification successful!');
        onSuccess(response.data.data, response.data.token);
        setStep('success');
        toast.success('Wallet connected successfully!');
      } else {
        setStatus(response.error || 'Invalid verification code');
      }
    } catch (error) {
      setStatus('Network error. Please try again.');
      onError?.('Network error during OTP verification');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (cooldown > 0) return;

    setIsLoading(true);
    setStatus('Resending verification code...');
    setCooldown(30); // 30 second cooldown

    try {
      const response = await apiService.socialWalletResendOTP(appId, { email });
      
      if (response.success) {
        setStatus('Verification code sent!');
        toast.success('New verification code sent to your email');
      } else {
        setStatus(response.error || 'Failed to resend code');
      }
    } catch (error) {
      setStatus('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleAuth = async () => {
    setIsLoading(true);
    setStatus('Redirecting to Google...');
    
    try {
      // This will redirect to Google OAuth
      await apiService.initiateGoogleAuth(appId);
    } catch (error) {
      setStatus('Failed to initiate Google authentication');
      setIsLoading(false);
      onError?.('Failed to initiate Google authentication');
    }
  };

  const renderSelectMethod = () => (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <div className="text-6xl mb-4">🔗</div>
        <h2 className="text-2xl font-bold mb-2">Connect Your Social Wallet</h2>
        <p className="text-muted-foreground">
          Choose your preferred authentication method to access your wallet
        </p>
      </div>

      <div className="space-y-3">
        <Button
          onClick={() => setStep('email-input')}
          className="w-full h-12 text-left justify-start"
          variant="outline"
        >
          <MailIcon className="mr-3 h-5 w-5" />
          <div>
            <div className="font-medium">Email Authentication</div>
            <div className="text-sm text-muted-foreground">Verify with email OTP</div>
          </div>
          <ArrowRightIcon className="ml-auto h-4 w-4" />
        </Button>

        <Button
          onClick={handleGoogleAuth}
          disabled={isLoading}
          className="w-full h-12 text-left justify-start"
          variant="outline"
        >
          <div className="mr-3 h-5 w-5 bg-gradient-to-r from-blue-500 to-red-500 rounded" />
          <div>
            <div className="font-medium">Google Authentication</div>
            <div className="text-sm text-muted-foreground">Sign in with Google</div>
          </div>
          <ArrowRightIcon className="ml-auto h-4 w-4" />
        </Button>
      </div>

      {status && (
        <div className="text-center text-sm text-muted-foreground mt-4">
          {status}
        </div>
      )}
    </div>
  );

  const renderEmailInput = () => (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <MailIcon className="mx-auto h-12 w-12 text-primary mb-4" />
        <h2 className="text-xl font-bold mb-2">Email Authentication</h2>
        <p className="text-muted-foreground">
          Enter your email to receive a verification code
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="email">Email Address</Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="<EMAIL>"
          disabled={isLoading}
        />
      </div>

      {status && (
        <div className={`text-sm text-center ${
          status.includes('error') || status.includes('Failed') 
            ? 'text-destructive' 
            : 'text-muted-foreground'
        }`}>
          {status}
        </div>
      )}

      <div className="flex space-x-2">
        <Button
          onClick={() => setStep('select')}
          variant="outline"
          disabled={isLoading}
          className="flex-1"
        >
          Back
        </Button>
        <Button
          onClick={handleEmailLogin}
          disabled={isLoading || !email}
          className="flex-1"
        >
          {isLoading ? 'Sending...' : 'Send Code'}
        </Button>
      </div>
    </div>
  );

  const renderEmailVerify = () => (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <ShieldCheckIcon className="mx-auto h-12 w-12 text-primary mb-4" />
        <h2 className="text-xl font-bold mb-2">Verify Your Email</h2>
        <p className="text-muted-foreground">
          Enter the 6-digit code sent to <strong>{email}</strong>
        </p>
        {walletExists && (
          <Badge variant="secondary" className="mt-2">
            Existing Wallet Found
          </Badge>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="otp">Verification Code</Label>
        <Input
          id="otp"
          type="text"
          value={otp}
          onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
          placeholder="000000"
          maxLength={6}
          disabled={isLoading}
          className="text-center text-lg tracking-widest"
        />
      </div>

      {status && (
        <div className={`text-sm text-center ${
          status.includes('error') || status.includes('Invalid') 
            ? 'text-destructive' 
            : 'text-muted-foreground'
        }`}>
          {status}
        </div>
      )}

      <div className="flex space-x-2">
        <Button
          onClick={() => setStep('email-input')}
          variant="outline"
          disabled={isLoading}
          className="flex-1"
        >
          Back
        </Button>
        <Button
          onClick={handleOTPVerification}
          disabled={isLoading || otp.length !== 6}
          className="flex-1"
        >
          {isLoading ? 'Verifying...' : 'Verify'}
        </Button>
      </div>

      <div className="text-center">
        <Button
          onClick={handleResendOTP}
          variant="ghost"
          size="sm"
          disabled={isLoading || cooldown > 0}
        >
          {cooldown > 0 ? (
            <>
              <RefreshCwIcon className="mr-2 h-4 w-4" />
              Resend in {cooldown}s
            </>
          ) : (
            <>
              <RefreshCwIcon className="mr-2 h-4 w-4" />
              Resend Code
            </>
          )}
        </Button>
      </div>
    </div>
  );

  const renderSuccess = () => (
    <div className="text-center space-y-4">
      <CheckCircleIcon className="mx-auto h-16 w-16 text-green-500" />
      <h2 className="text-xl font-bold">Wallet Connected!</h2>
      <p className="text-muted-foreground">
        Your social wallet has been successfully connected and authenticated.
      </p>
    </div>
  );

  return (
    <Card className={className}>
      <CardContent className="p-6">
        {step === 'select' && renderSelectMethod()}
        {step === 'email-input' && renderEmailInput()}
        {step === 'email-verify' && renderEmailVerify()}
        {step === 'success' && renderSuccess()}
      </CardContent>
    </Card>
  );
}
