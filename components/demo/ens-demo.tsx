'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  WalletIcon, 
  CheckCircleIcon, 
  ExternalLinkIcon,
  InfoIcon,
  ArrowRightIcon
} from "lucide-react";
import { CoinbaseWalletConnect } from "@/components/shared/coinbase-wallet-connect";
import { useCoinbaseWallet } from '@/lib/coinbase-wallet-provider';
import { useAuth } from "@/lib/auth-context";
import { apiService, Application } from "@/lib/api";

export function ENSDemo() {
  const [currentStep, setCurrentStep] = useState(1);
  const { isConnected, address } = useCoinbaseWallet();
  const { token } = useAuth();
  const [applications, setApplications] = useState<Application[]>([]);
  const [selectedApp, setSelectedApp] = useState<Application | null>(null);
  const [loading, setLoading] = useState(false);
  
  // Fetch applications from API
  const fetchApplications = useCallback(async () => {
    if (!token) return;
    
    try {
      setLoading(true);
      const response = await apiService.getApplications(token);
      
      if (response.success && response.data) {
        setApplications(response.data);
        
        // Set the first application as selected by default if there's one
        if (response.data.length > 0 && !selectedApp) {
          setSelectedApp(response.data[0]);
        }
      }
    } catch (error) {
      console.error('Failed to fetch applications:', error);
    } finally {
      setLoading(false);
    }
  }, [token, selectedApp]);
  
  // Automatically advance to step 2 when wallet is connected
  useEffect(() => {
    if (isConnected && currentStep === 1) {
      setCurrentStep(2);
    }
  }, [isConnected, currentStep]);
  
  // Fetch applications when component mounts
  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  const steps = [
    {
      id: 1,
      title: "Connect Wallet",
      description: "Connect your MetaMask or compatible Ethereum wallet",
      icon: WalletIcon,
      status: "completed"
    },
    {
      id: 2,
      title: "Select Project",
      description: "Choose which project to associate with your ENS name",
      icon: InfoIcon,
      status: currentStep >= 2 ? "completed" : "pending"
    },
    {
      id: 3,
      title: "Enter ENS Name",
      description: "Input your .eth domain name for verification",
      icon: CheckCircleIcon,
      status: currentStep >= 3 ? "completed" : "pending"
    },
    {
      id: 4,
      title: "Verify Ownership",
      description: "On-chain verification of ENS ownership",
      icon: CheckCircleIcon,
      status: currentStep >= 4 ? "completed" : "pending"
    }
  ];

  // Create connections based on actual applications and wallet
  const connections = applications.map(app => ({
    ensName: app.name.toLowerCase().replace(/\s+/g, '-') + '.eth',
    project: app.name,
    owner: address ? `${address.slice(0, 10)}...${address.slice(-5)}` : '0x0000...0000',
    status: 'active',
    avatar: Math.random() > 0.5, // Random for demo purposes
    appId: app.appId
  }));

  return (
    <div className="space-y-8 p-6">
      {/* Demo Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-[#4B0082] to-[#B497D6] bg-clip-text text-transparent mb-4">
          ENS Integration Demo
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          This demo showcases the ENS integration features. Connect your wallet, 
          select a project, and manage your ENS names seamlessly.
        </p>
      </div>

      {/* Process Steps */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold mb-6">Integration Process</h3>
        <div className="space-y-4">
          {steps.map((step, index) => {
            const Icon = step.icon;
            return (
              <div key={step.id} className="flex items-center gap-4">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  step.status === 'completed' 
                    ? 'bg-green-100 text-green-600' 
                    : 'bg-gray-100 text-gray-400'
                }`}>
                  <Icon className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium">{step.title}</h4>
                  <p className="text-sm text-gray-600">{step.description}</p>
                </div>
                <Badge className={
                  step.status === 'completed' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-600'
                }>
                  {step.status === 'completed' ? 'Completed' : 'Pending'}
                </Badge>
                {index < steps.length - 1 && (
                  <ArrowRightIcon className="h-4 w-4 text-gray-400 ml-2" />
                )}
              </div>
            );
          })}
        </div>
        
        {/* Rainbow Wallet Connect for Step 1 */}
        {currentStep === 1 && (
          <div className="mt-6">
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <InfoIcon className="inline h-4 w-4 mr-1" />
                Connect your wallet to continue with the ENS integration
              </p>
            </div>
            <CoinbaseWalletConnect
              className="w-full"
              onConnect={() => setCurrentStep(2)}
            />
          </div>
        )}
        
        {/* Application Selection for Step 2 */}
        {currentStep === 2 && (
          <div className="mt-6">
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <InfoIcon className="inline h-4 w-4 mr-1" />
                Select a project to associate with your ENS name
              </p>
            </div>
            
            {loading ? (
              <div className="text-center p-4">
                <div className="w-6 h-6 border-2 border-[#B497D6] border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                <p className="text-sm text-gray-600">Loading your applications...</p>
              </div>
            ) : applications.length === 0 ? (
              <div className="text-center p-4 border border-dashed border-gray-300 rounded-lg">
                <p className="text-sm text-gray-600 mb-2">No applications found</p>
                <Button 
                  onClick={() => window.location.href = '/dashboard/dashboard/applications'}
                  size="sm"
                  variant="outline"
                >
                  Create Application
                </Button>
              </div>
            ) : (
              <div className="space-y-2 max-h-60 overflow-y-auto pr-2">
                {applications.map((app) => (
                  <div
                    key={app.appId}
                    onClick={() => setSelectedApp(app)}
                    className={`p-3 rounded-lg cursor-pointer transition-all ${
                      selectedApp?.appId === app.appId
                        ? 'bg-[#B497D6]/20 border border-[#4B0082]/30'
                        : 'hover:bg-gray-50 border border-gray-100'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{app.name}</span>
                      {selectedApp?.appId === app.appId && (
                        <CheckCircleIcon className="h-4 w-4 text-[#4B0082]" />
                      )}
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      {app.allowedDomains.length > 0 ? 
                        `Allowed domains: ${app.allowedDomains.join(', ')}` : 
                        'No allowed domains'
                      }
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
        
        {/* ENS Name Input for Step 3 */}
        {currentStep === 3 && (
          <div className="mt-6">
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <InfoIcon className="inline h-4 w-4 mr-1" />
                Enter your ENS name to verify ownership
              </p>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Your Project</label>
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                  <div className="font-medium">{selectedApp?.name}</div>
                  <div className="text-xs text-gray-600 mt-1">
                    {selectedApp?.allowedDomains.length ? 
                      `Allowed domains: ${selectedApp?.allowedDomains.join(', ')}` : 
                      'No allowed domains'
                    }
                  </div>
                </div>
              </div>
              
              <div>
                <label htmlFor="ensName" className="block text-sm font-medium text-gray-700 mb-1">ENS Name</label>
                <div className="flex">
                  <input 
                    type="text"
                    id="ensName"
                    className="flex-1 p-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-[#4B0082]/30 focus:border-[#4B0082] outline-none"
                    placeholder="yourname"
                    defaultValue={selectedApp?.name.toLowerCase().replace(/\s+/g, '-')}
                  />
                  <div className="bg-gray-100 p-2 border-t border-r border-b border-gray-300 rounded-r-lg text-gray-600">
                    .eth
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Ownership Verification for Step 4 */}
        {currentStep === 4 && (
          <div className="mt-6">
            <div className="p-4 border border-green-200 bg-green-50 rounded-lg mb-4">
              <div className="flex items-center">
                <CheckCircleIcon className="h-6 w-6 text-green-600 mr-2" />
                <div>
                  <h4 className="font-medium text-green-800">Verification Successful</h4>
                  <p className="text-sm text-green-700">
                    {selectedApp?.name.toLowerCase().replace(/\s+/g, '-')}.eth has been linked to your project.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h4 className="font-medium mb-2">ENS Integration Details</h4>
              <div className="space-y-2 text-sm">
                <div className="grid grid-cols-3 gap-2">
                  <span className="text-gray-600">Project:</span>
                  <span className="col-span-2 font-medium">{selectedApp?.name}</span>
                </div>
                <div className="grid grid-cols-3 gap-2">
                  <span className="text-gray-600">ENS Name:</span>
                  <span className="col-span-2 font-medium">{selectedApp?.name.toLowerCase().replace(/\s+/g, '-')}.eth</span>
                </div>
                <div className="grid grid-cols-3 gap-2">
                  <span className="text-gray-600">Owner Address:</span>
                  <span className="col-span-2 font-mono text-xs">{address}</span>
                </div>
                <div className="grid grid-cols-3 gap-2">
                  <span className="text-gray-600">Status:</span>
                  <span className="col-span-2">
                    <Badge className="bg-green-100 text-green-800">
                      <CheckCircleIcon className="mr-1 h-3 w-3" />
                      active
                    </Badge>
                  </span>
                </div>
                <div className="grid grid-cols-3 gap-2">
                  <span className="text-gray-600">Verification Date:</span>
                  <span className="col-span-2">{new Date().toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Navigation buttons */}
        {currentStep > 1 && (
          <div className="mt-6 flex gap-3">
            <Button 
              onClick={() => setCurrentStep(Math.min(currentStep + 1, 4))}
              disabled={currentStep >= 4 || (currentStep === 2 && !selectedApp)}
              className="bg-[#4B0082] text-white hover:bg-opacity-90"
            >
              Next Step
            </Button>
            <Button 
              variant="outline"
              onClick={() => {
                setCurrentStep(1);
                setSelectedApp(null);
              }}
            >
              Reset Demo
            </Button>
          </div>
        )}
      </Card>

      {/* Connected ENS from Real Applications */}
      <div>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold">Connected ENS Names</h3>
          {isConnected ? (
            <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
              Wallet Connected
            </Badge>
          ) : (
            <Badge variant="outline" className="bg-gray-100 text-gray-600 border-gray-300">
              Wallet Not Connected
            </Badge>
          )}
        </div>
        
        {!isConnected && (
          <Card className="p-6 mb-4 border-dashed border-2">
            <div className="text-center">
              <WalletIcon className="h-10 w-10 mx-auto text-gray-400 mb-2" />
              <p className="text-gray-600 mb-4">Connect your wallet to view your ENS names</p>
              <CoinbaseWalletConnect
                className="mx-auto"
              />
            </div>
          </Card>
        )}
        
        {isConnected && loading && (
          <Card className="p-6 mb-4">
            <div className="text-center">
              <div className="w-8 h-8 border-4 border-[#B497D6] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-600">Loading your applications...</p>
            </div>
          </Card>
        )}
        
        {isConnected && !loading && applications.length === 0 && (
          <Card className="p-6 mb-4 border-dashed border-2">
            <div className="text-center">
              <InfoIcon className="h-10 w-10 mx-auto text-amber-500 mb-2" />
              <p className="text-gray-600 mb-4">No applications found. Create applications in your dashboard to connect with ENS.</p>
              <Button 
                onClick={() => window.location.href = '/dashboard/dashboard/applications'}
                className="bg-[#4B0082] text-white hover:bg-opacity-90"
              >
                Create Application
              </Button>
            </div>
          </Card>
        )}
        
        {isConnected && !loading && connections.length > 0 && (
          <div className="grid gap-4 md:grid-cols-2">
            {connections.map((connection) => (
              <Card key={connection.appId} className="p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h4 className="font-semibold text-lg text-[#4B0082]">
                      {connection.ensName}
                    </h4>
                    <p className="text-sm text-gray-600">{connection.project}</p>
                  </div>
                  {connection.avatar && (
                    <div className="w-8 h-8 bg-gradient-to-br from-[#4B0082] to-[#B497D6] rounded-full flex items-center justify-center text-white text-xs font-semibold">
                      APP
                    </div>
                  )}
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Owner:</span>
                    <span className="font-mono">{connection.owner}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <Badge className="bg-green-100 text-green-800">
                      <CheckCircleIcon className="mr-1 h-3 w-3" />
                      {connection.status}
                    </Badge>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Feature Highlights */}
      <Card className="p-6 bg-gradient-to-r from-[#4B0082]/5 to-[#B497D6]/5">
        <h3 className="text-xl font-semibold mb-4">Key Features</h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <div className="flex items-start gap-3">
            <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5" />
            <div>
              <h4 className="font-medium">On-chain Verification</h4>
              <p className="text-sm text-gray-600">Direct ENS Registry contract interaction</p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5" />
            <div>
              <h4 className="font-medium">Project Association</h4>
              <p className="text-sm text-gray-600">Link ENS names to specific projects</p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5" />
            <div>
              <h4 className="font-medium">Ownership Transfer</h4>
              <p className="text-sm text-gray-600">Transfer ENS to other wallets</p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5" />
            <div>
              <h4 className="font-medium">Avatar Support</h4>
              <p className="text-sm text-gray-600">Display ENS avatars and NFTs</p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5" />
            <div>
              <h4 className="font-medium">Network Detection</h4>
              <p className="text-sm text-gray-600">Auto-switch to Ethereum Mainnet</p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5" />
            <div>
              <h4 className="font-medium">Real-time Feedback</h4>
              <p className="text-sm text-gray-600">Toast notifications and status updates</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Call to Action */}
      <div className="text-center">
        {!isConnected ? (
          <>
            <p className="text-lg font-medium mb-3">Connect Your Wallet to Get Started</p>
            <CoinbaseWalletConnect
              className="mx-auto mb-4"
            />
          </>
        ) : (
          <>
            <Button 
              onClick={() => window.open('https://app.ens.domains', '_blank')}
              className="bg-[#4B0082] text-white hover:bg-opacity-90"
            >
              <ExternalLinkIcon className="mr-2 h-4 w-4" />
              Claim Your ENS Name
            </Button>
          </>
        )}
        <p className="text-sm text-gray-600 mt-2">
          Don&apos;t have an ENS name yet? Get one at app.ens.domains
        </p>
      </div>
    </div>
  );
}