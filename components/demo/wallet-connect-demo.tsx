'use client';

import { useState } from 'react';
import { CoinbaseWalletConnect } from '@/components/shared/coinbase-wallet-connect';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function WalletConnectDemo() {
  const [connectedAddress, setConnectedAddress] = useState<string | null>(null);
  
  const handleConnect = (address: string) => {
    setConnectedAddress(address);
  };
  
  const handleDisconnect = () => {
    setConnectedAddress(null);
  };
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8 text-center">Coinbase Wallet Connection Demo</h1>

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Coinbase Wallet Connect</CardTitle>
            <CardDescription>
              Dedicated Coinbase Wallet connection experience
            </CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <CoinbaseWalletConnect
              onConnect={handleConnect}
              onDisconnect={handleDisconnect}
            />
          </CardContent>
        </Card>
      </div>
      
      {connectedAddress && (
        <div className="mt-8 p-4 bg-green-50 border border-green-200 rounded-lg max-w-2xl mx-auto">
          <p className="text-center text-green-800">
            Connected with address: <code className="font-mono">{connectedAddress.slice(0, 6)}...{connectedAddress.slice(-4)}</code>
          </p>
        </div>
      )}
    </div>
  );
}
