'use client';

import { useState, useEffect } from 'react';
import { PrismAsyncLight as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/cjs/styles/prism';
// Supporting a few common languages for highlighting
import jsx from 'react-syntax-highlighter/dist/cjs/languages/prism/jsx';
import javascript from 'react-syntax-highlighter/dist/cjs/languages/prism/javascript';
import bash from 'react-syntax-highlighter/dist/cjs/languages/prism/bash';
import json from 'react-syntax-highlighter/dist/cjs/languages/prism/json';

import { motion } from 'framer-motion';
import { Copy, Check, Terminal, FileCode, ChevronRight, ExternalLink } from 'lucide-react'; // Added more icons

// Register languages
SyntaxHighlighter.registerLanguage('jsx', jsx);
SyntaxHighlighter.registerLanguage('javascript', javascript);
SyntaxHighlighter.registerLanguage('bash', bash);
SyntaxHighlighter.registerLanguage('json', json);

const exampleCode = `
// Example: Verifying a credential using Crefy Connect SDK
import { CrefySDK } from '@crefy/sdk';

async function verifyUserCredential(userId, credentialType) {
  const sdk = new CrefySDK({ apiKey: 'YOUR_API_KEY' });

  try {
    const credential = await sdk.getCredential(userId, credentialType);
    if (credential && credential.isValid) {
      console.log(\`Credential '\${credentialType}' for user \${userId} is valid.\`);
      return true;
    } else {
      console.warn(\`Credential '\${credentialType}' for user \${userId} not found or invalid.\`);
      return false;
    }
  } catch (error) {
    console.error("Error verifying credential:", error);
    return false;
  }
}

// Usage
verifyUserCredential('user_123', 'ProofOfAge')
  .then(isValid => console.log("Verification result:", isValid));
`.trim();

// Sample code snippets for tabs
const codeSnippets = {
  javascript: exampleCode,
  python: `
# Example: Verifying a credential using Crefy Connect SDK
from crefy.sdk import CrefySDK

def verify_user_credential(user_id, credential_type):
    sdk = CrefySDK(api_key='YOUR_API_KEY')
    
    try:
        credential = sdk.get_credential(user_id, credential_type)
        if credential and credential.is_valid:
            print(f"Credential '{credential_type}' for user {user_id} is valid.")
            return True
        else:
            print(f"Credential '{credential_type}' for user {user_id} not found or invalid.")
            return False
    except Exception as error:
        print(f"Error verifying credential: {error}")
        return False

# Usage
result = verify_user_credential('user_123', 'ProofOfAge')
print(f"Verification result: {result}")
`.trim(),
  curl: `
# Example: Verifying a credential using Crefy Connect API
curl -X GET "https://api.crefy.com/v1/credentials/user_123/ProofOfAge" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json"
`.trim()
};

const simulatedApiResponse = `
{
  "userId": "user_123",
  "credentialType": "ProofOfAge",
  "isValid": true,
  "issuedAt": "2024-01-15T10:30:00Z",
  "expiresAt": "2025-01-14T23:59:59Z",
  "issuer": "did:crefy:123abc456xyz"
}
`.trim();


// Custom hook for typewriter effect
const useTypewriter = (text: string, speed: number = 50, loop: boolean = false) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    if (!text) return;

    const handleTyping = () => {
      if (!isDeleting && currentIndex < text.length) {
        setDisplayText((prev) => prev + text.charAt(currentIndex));
        setCurrentIndex((prev) => prev + 1);
      } else if (isDeleting && currentIndex > 0) {
        setDisplayText((prev) => prev.substring(0, prev.length -1));
        setCurrentIndex((prev) => prev -1);
      } else if (!isDeleting && currentIndex === text.length && loop) {
        setTimeout(() => setIsDeleting(true), 1000); // Pause before deleting
      } else if (isDeleting && currentIndex === 0 && loop) {
        setIsDeleting(false);
        // Optionally, change text or reset here if looping through multiple texts
      }
    };

    const timer = setTimeout(handleTyping, isDeleting ? speed / 2 : speed);
    return () => clearTimeout(timer);
  }, [currentIndex, isDeleting, text, speed, loop]);

  // Allow manually resetting the typewriter for new text
  const startTyping = () => {
    setDisplayText('');
    setCurrentIndex(0);
    setIsDeleting(false);
  };

  return { displayText, startTyping };
};


const DeveloperCodeSection = () => {
  const [copied, setCopied] = useState(false);
  const [activeTab, setActiveTab] = useState('javascript');
  const { displayText: apiResponseText, startTyping: startApiResponseTyping } = useTypewriter(simulatedApiResponse, 20);

  useEffect(() => {
    // Trigger typewriter when component mounts or when active tab changes
    startApiResponseTyping();
  }, [activeTab]); // Re-run if activeTab changes

  const handleCopy = () => {
    navigator.clipboard.writeText(codeSnippets[activeTab]).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  return (
    <section
      id="developers"
      className="bg-white px-4 md:px-12 py-16 group" // Changed to pure white background
    >
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.2 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-10"
        >
          <h2 className="text-4xl lg:text-5xl font-heading font-bold text-black">
            Integrate With Ease
          </h2>
          <p className="text-lg text-black mt-4 max-w-2xl mx-auto">
            Our developer-friendly tools and clear documentation make integrating Crefy Connect a breeze.
          </p>
        </motion.div>

        <motion.div
          className="
            grid lg:grid-cols-5 gap-8 items-stretch
            bg-white border border-[#e0e0e0] rounded-xl p-2 md:p-6
            group-hover:shadow-lg transition-shadow duration-300
          "
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.5 }}
        >
          {/* Left Column - Code Navigation */}
          <div className="lg:col-span-1 bg-white rounded-lg border-r border-[#e0e0e0] pr-4">
            <h3 className="font-medium text-[#4a148c] mb-4 flex items-center">
              <FileCode size={18} className="mr-2" /> Example Code
            </h3>
            <nav className="flex flex-col space-y-1">
              <button 
                onClick={() => setActiveTab('javascript')}
                className={`px-3 py-2 text-left rounded-md flex items-center text-sm font-medium ${
                  activeTab === 'javascript' 
                    ? 'bg-[#f5f0ff] text-[#4a148c] font-semibold' 
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {activeTab === 'javascript' && <ChevronRight size={16} className="mr-1" />}
                JavaScript
              </button>
              <button 
                onClick={() => setActiveTab('python')}
                className={`px-3 py-2 text-left rounded-md flex items-center text-sm font-medium ${
                  activeTab === 'python' 
                    ? 'bg-[#f5f0ff] text-[#4a148c] font-semibold' 
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {activeTab === 'python' && <ChevronRight size={16} className="mr-1" />}
                Python
              </button>
              <button 
                onClick={() => setActiveTab('curl')}
                className={`px-3 py-2 text-left rounded-md flex items-center text-sm font-medium ${
                  activeTab === 'curl' 
                    ? 'bg-[#f5f0ff] text-[#4a148c] font-semibold' 
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {activeTab === 'curl' && <ChevronRight size={16} className="mr-1" />}
                cURL
              </button>
            </nav>
            
            <div className="mt-6 pt-6 border-t border-[#e0e0e0]">
              <a 
                href="#" 
                className="flex items-center text-sm text-[#673ab7] hover:text-[#4a148c] transition-colors"
              >
                <ExternalLink size={14} className="mr-1.5" />
                View Documentation
              </a>
            </div>
          </div>

          {/* Middle Column - Code Block */}
          <div className="lg:col-span-2 bg-[#1e1e1e] rounded-lg overflow-hidden relative h-full flex flex-col">
            <div className="flex justify-between items-center px-4 py-2 bg-[#2d2d2d]">
              <span className="text-sm text-gray-400">{activeTab === 'javascript' ? 'example.js' : activeTab === 'python' ? 'example.py' : 'example.sh'}</span>
              <button
                onClick={handleCopy}
                title={copied ? "Copied!" : "Copy code"}
                className="text-gray-400 hover:text-white transition-colors p-1 rounded"
              >
                {copied ? <Check size={18} className="text-green-400" /> : <Copy size={18} />}
              </button>
            </div>
            <div className="overflow-auto p-1 flex-grow min-h-[320px]">
              <SyntaxHighlighter
                language={activeTab === 'curl' ? 'bash' : activeTab}
                style={oneDark}
                customStyle={{
                  backgroundColor: 'transparent',
                  padding: '1rem',
                  margin: '0',
                  fontSize: '0.875rem', // 14px
                  height: '100%',
                  overflowX: 'auto',
                }}
                showLineNumbers={true}
                lineNumberStyle={{ color: '#5C6370', fontSize: '0.75rem', marginRight: '1rem' }}
                wrapLines={true}
                codeTagProps={{ style: { fontFamily: '"Space Mono", monospace' } }}
              >
                {codeSnippets[activeTab]}
              </SyntaxHighlighter>
            </div>
          </div>

          {/* Right Column - Simulated Terminal/API Response */}
          <div className="lg:col-span-2 bg-[#1e1e1e] rounded-lg p-6 flex flex-col h-full min-h-[320px]">
            <div className="flex items-center mb-4">
              <Terminal size={16} className="text-[#9575cd] mr-2" />
              <span className="text-sm font-medium text-gray-300">Response Preview</span>
              <div className="ml-auto flex items-center">
                <div className="w-3 h-3 rounded-full bg-red-500 mr-1.5"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500 mr-1.5"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
            </div>
            <pre className="text-sm text-gray-300 whitespace-pre-wrap overflow-auto flex-grow font-mono bg-[#262626] p-4 rounded-md border border-[#333]">
              <code>
                {apiResponseText}
                <span className="animate-pulse text-[#9575cd]">▋</span> {/* Blinking cursor with theme color */}
              </code>
            </pre>
            <div className="mt-4 text-xs text-gray-500 flex items-center">
              <div className="w-2 h-2 rounded-full bg-[#9575cd] mr-2 animate-pulse"></div>
              Live response from API endpoint
            </div>
          </div>
        </motion.div>
        
        <div className="mt-10 text-center">
          <a 
            href="#" 
            className="inline-flex items-center px-5 py-2.5 rounded-full bg-white border border-[#4a148c] text-[#4a148c] hover:bg-[#f5f0ff] transition-colors font-medium"
          >
            Explore API Documentation
            <ChevronRight size={18} className="ml-1" />
          </a>
        </div>
      </div>
    </section>
  );
};

export default DeveloperCodeSection;