'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Toggle mobile menu
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const navLinkClasses = "px-3 py-2 hover:scale-105 transform transition-transform duration-200 ease-in-out text-gray-700 hover:text-purple-800";

  return (
    <nav className="fixed top-4 left-1/2 -translate-x-1/2 z-50 w-full max-w-[95%] md:max-w-[800px] lg:max-w-[900px] transition-all duration-300 ease-in-out">
      <div className={`
        mx-auto px-6 py-4 rounded-full flex items-center justify-between
        bg-transparent backdrop-blur-sm border border-gray-200
        ${isScrolled ? 'shadow-md bg-white/80' : ''}
      `}>
        <Link href="/" className="text-2xl font-heading font-bold bg-gradient-to-r from-purple-900 to-purple-700 bg-clip-text text-transparent">
          Crefy Connect
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center justify-center flex-1 space-x-4">
          <Link href="/#home" className={navLinkClasses}>Home</Link>
          <Link href="/#features" className={navLinkClasses}>Features</Link>
          <Link href="/#docs" className={navLinkClasses}>Docs</Link>
        </div>

        {/* Right-aligned buttons for login & sign up */}
        <div className="hidden md:flex items-center space-x-3">
          <Link href="/setup" className={`${navLinkClasses} text-sm`}>Setup</Link>
          <Link href="/auth/wallet-connect" className={`${navLinkClasses} border border-gray-300 rounded-full px-4 hover:border-purple-800 hover:text-purple-800`}>Connect Wallet</Link>
          <Link href="/auth/signin" className="bg-purple-900 text-white px-4 py-2 rounded-full hover:bg-purple-800 transition-colors">Developer Login</Link>
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <button 
            onClick={toggleMenu}
            className="text-gray-700 focus:outline-none"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
              <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Menu (only visible when open) */}
      {isMenuOpen && (
        <div className="md:hidden mt-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-lg p-4 border border-purple-100">
          <div className="flex flex-col space-y-2">
            <Link href="/#home" className={navLinkClasses} onClick={toggleMenu}>Home</Link>
            <Link href="/#features" className={navLinkClasses} onClick={toggleMenu}>Features</Link>
            <Link href="/#docs" className={navLinkClasses} onClick={toggleMenu}>Docs</Link>
            <Link href="/auth/wallet-connect" className={`${navLinkClasses} border border-gray-300 rounded-full px-4 hover:border-purple-800 hover:text-purple-800`} onClick={toggleMenu}>Connect Wallet</Link>
            <Link href="/auth/signin" className="bg-purple-900 text-white px-4 py-2 rounded-full hover:bg-purple-800 transition-colors" onClick={toggleMenu}>Developer Login</Link>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;