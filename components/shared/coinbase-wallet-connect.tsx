'use client';

import { useState } from 'react';
import { useCoinbaseWallet } from '@/lib/coinbase-wallet-provider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Modal } from '@/components/shared/modal';
import { 
  WalletIcon, 
  ExternalLinkIcon, 
  CopyIcon, 
  CheckIcon,
  LogOutIcon,
  ShieldCheckIcon,
  AlertCircleIcon
} from 'lucide-react';

interface CoinbaseWalletConnectProps {
  showDisconnect?: boolean;
  className?: string;
  onConnect?: (address: string) => void;
  onDisconnect?: () => void;
}

export function CoinbaseWalletConnect({ 
  showDisconnect = true, 
  className = '', 
  onConnect, 
  onDisconnect 
}: CoinbaseWalletConnectProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [copied, setCopied] = useState(false);
  
  const { 
    isConnected, 
    address, 
    chainId, 
    isConnecting, 
    error, 
    connect, 
    disconnect,
    switchChain 
  } = useCoinbaseWallet();

  const handleConnect = async () => {
    try {
      await connect();
      if (address && onConnect) {
        onConnect(address);
      }
      setIsModalOpen(false);
    } catch (error) {
      console.error('Failed to connect:', error);
    }
  };

  const handleDisconnect = async () => {
    try {
      await disconnect();
      if (onDisconnect) {
        onDisconnect();
      }
    } catch (error) {
      console.error('Failed to disconnect:', error);
    }
  };

  const copyAddress = async () => {
    if (address) {
      await navigator.clipboard.writeText(address);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const shortenAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const getChainName = (id: number) => {
    switch (id) {
      case 1: return 'Ethereum Mainnet';
      case 11155111: return 'Sepolia Testnet';
      case 137: return 'Polygon';
      case 56: return 'BSC';
      case 43114: return 'Avalanche';
      case 42161: return 'Arbitrum';
      case 10: return 'Optimism';
      default: return `Chain ${id}`;
    }
  };

  const handleSwitchToMainnet = async () => {
    try {
      await switchChain(1);
    } catch (error) {
      console.error('Failed to switch chain:', error);
    }
  };

  const handleSwitchToSepolia = async () => {
    try {
      await switchChain(11155111);
    } catch (error) {
      console.error('Failed to switch chain:', error);
    }
  };

  if (isConnected && address) {
    return (
      <Card className={`w-full max-w-md ${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <ShieldCheckIcon className="h-5 w-5 text-green-600" />
            Coinbase Wallet Connected
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-800">Connected</span>
              </div>
              <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
                {getChainName(chainId || 1)}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="flex items-center gap-2">
                <WalletIcon className="h-4 w-4 text-gray-600" />
                <code className="text-sm font-mono text-gray-800">
                  {shortenAddress(address)}
                </code>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={copyAddress}
                className="h-8 w-8 p-0 hover:bg-gray-200"
              >
                {copied ? (
                  <CheckIcon className="h-4 w-4 text-green-600" />
                ) : (
                  <CopyIcon className="h-4 w-4 text-gray-600" />
                )}
              </Button>
            </div>

            {/* Chain switching buttons */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSwitchToMainnet}
                className="flex-1 text-xs"
                disabled={chainId === 1}
              >
                Mainnet
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSwitchToSepolia}
                className="flex-1 text-xs"
                disabled={chainId === 11155111}
              >
                Sepolia
              </Button>
            </div>
          </div>
          
          {showDisconnect && (
            <Button
              variant="outline"
              onClick={handleDisconnect}
              className="w-full flex items-center gap-2 hover:bg-red-50 hover:border-red-200 hover:text-red-700"
            >
              <LogOutIcon className="h-4 w-4" />
              Disconnect Wallet
            </Button>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Button
        onClick={() => setIsModalOpen(true)}
        className={`flex items-center gap-2 ${className}`}
        disabled={isConnecting}
      >
        <WalletIcon className="h-4 w-4" />
        {isConnecting ? 'Connecting...' : 'Connect Coinbase Wallet'}
      </Button>

      <Modal
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        title="Connect Coinbase Wallet"
        actionLabel=""
      >
        <div className="space-y-6">
          <div className="text-center">
            <div className="text-6xl mb-4">🔵</div>
            <h3 className="text-lg font-semibold mb-2">Coinbase Wallet</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Connect your Coinbase Wallet to access your account securely.
            </p>
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-xs text-blue-800">
                <ShieldCheckIcon className="inline h-3 w-3 mr-1" />
                Your wallet remains secure. We never store your private keys.
              </p>
            </div>
          </div>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertCircleIcon className="h-4 w-4 text-red-600" />
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          )}
          
          <div className="space-y-3">
            <Button
              variant="outline"
              className="w-full justify-start h-16 hover:bg-gray-50 border-2 hover:border-blue-200 transition-all"
              onClick={handleConnect}
              disabled={isConnecting}
            >
              <div className="flex items-center gap-4 w-full">
                <div className="text-2xl">🔵</div>
                <div className="text-left flex-1">
                  <div className="font-semibold text-base">Coinbase Wallet</div>
                  <div className="text-xs text-muted-foreground">
                    {isConnecting ? 'Connecting...' : 'Connect with Coinbase Wallet'}
                  </div>
                </div>
                <div className="text-gray-400">→</div>
              </div>
            </Button>
          </div>

          <div className="pt-4 border-t border-gray-200">
            <div className="text-center space-y-2">
              <p className="text-xs text-muted-foreground">Don&apos;t have Coinbase Wallet?</p>
              <div className="flex justify-center gap-4">
                <Button
                  variant="link"
                  className="p-0 h-auto text-xs"
                  onClick={() => window.open('https://www.coinbase.com/wallet', '_blank')}
                >
                  Get Coinbase Wallet
                  <ExternalLinkIcon className="ml-1 h-3 w-3" />
                </Button>
                <Button
                  variant="link"
                  className="p-0 h-auto text-xs"
                  onClick={() => window.open('https://chrome.google.com/webstore/detail/coinbase-wallet-extension/hnfanknocfeofbddgcijnmhnfnkdnaad', '_blank')}
                >
                  Browser Extension
                  <ExternalLinkIcon className="ml-1 h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
}