import { Modal } from "@/components/shared/modal"
import { CoinbaseWalletConnect } from '@/components/shared/coinbase-wallet-connect'
import { useCoinbaseWallet } from '@/lib/coinbase-wallet-provider'
import { useEffect } from 'react'

interface ConnectWalletModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConnect: (provider: string) => void
}

export function ConnectWalletModal({
  open,
  onOpenChange,
  onConnect
}: ConnectWalletModalProps) {
  const { isConnected, address } = useCoinbaseWallet()

  // Close modal when connected
  useEffect(() => {
    if (isConnected && address) {
      onOpenChange(false)
      onConnect(address)
    }
  }, [isConnected, address, onOpenChange, onConnect])

  return (
    <Modal
      open={open}
      onOpenChange={onOpenChange}
      title="Connect Your Coinbase Wallet"
      actionLabel=""
    >
      <div className="space-y-4">
        <div className="text-center">
          <div className="text-6xl mb-4">🔗</div>
          <p className="text-sm text-muted-foreground">
            Connect your Coinbase Wallet to access your account securely
          </p>
        </div>
        <div className="flex justify-center py-4">
          <CoinbaseWalletConnect
            onConnect={(address) => onConnect(address)}
            onDisconnect={() => {}}
            showDisconnect={false}
          />
        </div>
      </div>
    </Modal>
  )
}