"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Menu, LayoutDashboard, BadgeCheck, Settings, UserCircle, KeyRound } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

const navItems = [
  { name: "Applications", path: "/dashboard/dashboard/applications", icon: LayoutDashboard },
  { name: "ENS Names", path: "/dashboard/dashboard/ens", icon: BadgeCheck },
  { name: "API Keys", path: "/dashboard/dashboard/api-keys", icon: KeyRound },
  { name: "Profile", path: "/dashboard/dashboard/profile", icon: UserCircle },
  { name: "Settings", path: "/dashboard/dashboard/settings", icon: Settings },
  // { name: "Wallets", path: "/dashboard/dashboard/wallets", icon: Wallet },
  // { name: "Webhooks", path: "/dashboard/dashboard/webhooks", icon: Webhook },
]

export function DashboardSidebar() {
  const pathname = usePathname()

  return (
    <>
      {/* Mobile Sidebar */}
      <Sheet>
        <SheetTrigger asChild className="lg:hidden">
          <Button variant="outline" size="icon">
            <Menu className="h-5 w-5" />
          </Button>
        </SheetTrigger>
        <SheetContent className="w-[250px] sm:w-[300px]">
          <nav className="flex flex-col gap-4">
            {navItems.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.path
              return (
                <Link
                  key={item.path}
                  href={item.path}
                  className={`flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    isActive 
                      ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white shadow-lg' 
                      : 'hover:bg-[#7B1FA2]/10 hover:text-[#4A148C]'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  {item.name}
                </Link>
              )
            })}
          </nav>
        </SheetContent>
      </Sheet>

      {/* Desktop Sidebar */}
      <aside className="hidden lg:block w-[280px] h-screen sticky top-0 p-4">
        <div className="h-full bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-3xl shadow-2xl hover:shadow-3xl transition-all duration-500 overflow-hidden relative">
          {/* Gradient overlay for visual effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#4B0082]/5 via-transparent to-[#B497D6]/5 pointer-events-none"></div>
          
          {/* Animated background elements */}
          <div className="absolute top-4 right-4 w-20 h-20 bg-[#7B1FA2]/10 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute bottom-8 left-4 w-16 h-16 bg-[#4A148C]/10 rounded-full blur-xl animate-bounce-slow"></div>
          
          <div className="relative z-10 flex flex-col h-full p-6">
            {/* Logo/Brand */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                Crefy Connect
              </h2>
              <p className="text-sm text-gray-500 mt-1">Developer Dashboard</p>
            </div>
            
            {/* Navigation */}
            <nav className="flex flex-col gap-3 flex-1">
              {navItems.map((item) => {
                const Icon = item.icon
                const isActive = pathname === item.path
                return (
                  <Link
                    key={item.path}
                    href={item.path}
                    className={`group flex items-center gap-4 px-4 py-3 rounded-2xl text-sm font-medium transition-all duration-300 transform hover:scale-105 ${
                      isActive 
                        ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white shadow-lg shadow-[#4A148C]/25' 
                        : 'hover:bg-[#7B1FA2]/10 hover:text-[#4A148C] hover:shadow-md'
                    }`}
                  >
                    <Icon className={`h-5 w-5 transition-transform duration-300 ${
                      isActive ? 'scale-110' : 'group-hover:scale-110'
                    }`} />
                    <span className="font-medium">{item.name}</span>
                    {isActive && (
                      <div className="ml-auto w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    )}
                  </Link>
                )
              })}
            </nav>
            
            {/* Footer */}
            <div className="mt-auto pt-6 border-t border-gray-200/50">
              <div className="text-xs text-gray-400 text-center">
                <p>© 2025 Crefy Connect</p>
                <p className="mt-1">v1.0.0</p>
              </div>
            </div>
          </div>
        </div>
      </aside>
    </>
  )
}