"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth-context";
import { apiService } from "@/lib/api";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { User, RefreshCw } from "lucide-react";

export function UserProfile() {
  const { user, token, refreshProfile } = useAuth();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [profileData, setProfileData] = useState(user);

  const handleRefreshProfile = async () => {
    if (!user || !token) return;

    setIsRefreshing(true);
    try {
      const response = await apiService.getProfile(user.id, token);
      if (response.success && response.data) {
        setProfileData({ ...response.data, role: user.role });
        await refreshProfile();
      }
    } catch (error) {
      console.error("Failed to refresh profile:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    setProfileData(user);
  }, [user]);

  if (!profileData) {
    return null;
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-medium">Profile</CardTitle>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefreshProfile}
          disabled={isRefreshing}
          className="h-8 w-8 p-0"
        >
          <RefreshCw className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
            <User className="h-5 w-5 text-primary" />
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium">{profileData.email}</p>
            <p className="text-xs text-muted-foreground capitalize">
              {profileData.role}
            </p>
          </div>
        </div>
        
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">User ID:</span>
            <span className="font-mono text-xs">{profileData.id}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Verified:</span>
            <span className={profileData.isVerified ? "text-green-600" : "text-red-600"}>
              {profileData.isVerified ? "Yes" : "No"}
            </span>
          </div>
          {profileData.createdAt && (
            <div className="flex justify-between">
              <span className="text-muted-foreground">Joined:</span>
              <span className="text-xs">
                {new Date(profileData.createdAt).toLocaleDateString()}
              </span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}