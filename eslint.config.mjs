// Using ESLint v8 compatibility mode
import typescript from 'typescript-eslint';
import react from 'eslint-plugin-react';

export default [
  {
    ignores: ["**/node_modules/**", "**/.next/**", "**/dist/**"],
  },
  {
    files: ["**/*.{js,mjs,cjs,jsx,ts,tsx}"],
    plugins: {
      react
    },
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      parserOptions: {
        ecmaFeatures: {
          jsx: true
        }
      },
    },
    rules: {
      // Base ESLint rules
      'no-unused-vars': 'warn',
      'no-console': 'warn',
    }
  },
  // typescript-eslint config
  ...typescript.configs.recommended
];
