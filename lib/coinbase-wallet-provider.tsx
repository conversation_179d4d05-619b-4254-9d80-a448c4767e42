'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define types for Coinbase Wallet
interface CoinbaseWallet {
  request: (args: { method: string; params?: any[] }) => Promise<any>;
  on: (event: string, handler: (...args: any[]) => void) => void;
  removeListener: (event: string, handler: (...args: any[]) => void) => void;
  isConnected: () => boolean;
  selectedAddress: string | null;
  chainId: string | null;
}

interface WalletState {
  isConnected: boolean;
  address: string | null;
  chainId: number | null;
  isConnecting: boolean;
  error: string | null;
}

interface WalletContextType extends WalletState {
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  switchChain: (chainId: number) => Promise<void>;
}

const WalletContext = createContext<WalletContextType | undefined>(undefined);

interface CoinbaseWalletProviderProps {
  children: ReactNode;
  appName?: string;
  appLogoUrl?: string;
  darkMode?: boolean;
}

export function CoinbaseWalletProvider({ 
  children, 
  appName = 'Crefy Connect',
  appLogoUrl,
  darkMode = false 
}: CoinbaseWalletProviderProps) {
  const [walletState, setWalletState] = useState<WalletState>({
    isConnected: false,
    address: null,
    chainId: null,
    isConnecting: false,
    error: null,
  });

  const [wallet, setWallet] = useState<CoinbaseWallet | null>(null);

  // Initialize Coinbase Wallet
  useEffect(() => {
    const initializeWallet = async () => {
      try {
        // Check if Coinbase Wallet is available
        if (typeof window !== 'undefined') {
          // Try to get Coinbase Wallet from window.ethereum
          const ethereum = (window as any).ethereum;
          
          if (ethereum && ethereum.isCoinbaseWallet) {
            setWallet(ethereum);
            
            // Check if already connected
            const accounts = await ethereum.request({ method: 'eth_accounts' });
            const chainId = await ethereum.request({ method: 'eth_chainId' });
            
            if (accounts.length > 0) {
              setWalletState({
                isConnected: true,
                address: accounts[0],
                chainId: parseInt(chainId, 16),
                isConnecting: false,
                error: null,
              });
            }
          } else {
            // Fallback: Try to initialize Coinbase Wallet SDK
            try {
              const { CoinbaseWalletSDK } = await import('@coinbase/wallet-sdk');
              
              const coinbaseWallet = new CoinbaseWalletSDK({
                appName,
                appLogoUrl,
                // TypeScript doesn't recognize darkMode in the type definition
                // but it's actually supported by the SDK
                ...((darkMode !== undefined) && { darkMode } as any),
              });

              const provider = coinbaseWallet.makeWeb3Provider();
              setWallet(provider as any);

              // Check if already connected
              const accounts = await provider.request({ method: 'eth_accounts' }) as string[];
              const chainId = await provider.request({ method: 'eth_chainId' });
              
              if (accounts.length > 0) {
                setWalletState({
                  isConnected: true,
                  address: accounts[0],
                  chainId: parseInt(chainId as string, 16),
                  isConnecting: false,
                  error: null,
                });
              }
            } catch (sdkError) {
              console.warn('Coinbase Wallet SDK not available:', sdkError);
              setWalletState(prev => ({
                ...prev,
                error: 'Coinbase Wallet not available. Please install the Coinbase Wallet extension or app.',
              }));
            }
          }
        }
      } catch (error) {
        console.error('Failed to initialize wallet:', error);
        setWalletState(prev => ({
          ...prev,
          error: 'Failed to initialize wallet connection.',
        }));
      }
    };

    initializeWallet();
  }, [appName, appLogoUrl, darkMode]);

  // Set up event listeners
  useEffect(() => {
    if (!wallet) return;

    const handleAccountsChanged = (accounts: string[]) => {
      if (accounts.length === 0) {
        setWalletState(prev => ({
          ...prev,
          isConnected: false,
          address: null,
        }));
      } else {
        setWalletState(prev => ({
          ...prev,
          isConnected: true,
          address: accounts[0],
        }));
      }
    };

    const handleChainChanged = (chainId: string) => {
      setWalletState(prev => ({
        ...prev,
        chainId: parseInt(chainId, 16),
      }));
    };

    const handleDisconnect = () => {
      setWalletState(prev => ({
        ...prev,
        isConnected: false,
        address: null,
        chainId: null,
      }));
    };

    wallet.on('accountsChanged', handleAccountsChanged);
    wallet.on('chainChanged', handleChainChanged);
    wallet.on('disconnect', handleDisconnect);

    return () => {
      wallet.removeListener('accountsChanged', handleAccountsChanged);
      wallet.removeListener('chainChanged', handleChainChanged);
      wallet.removeListener('disconnect', handleDisconnect);
    };
  }, [wallet]);

  const connect = async () => {
    if (!wallet) {
      setWalletState(prev => ({
        ...prev,
        error: 'Wallet not initialized. Please install Coinbase Wallet.',
      }));
      return;
    }

    setWalletState(prev => ({ ...prev, isConnecting: true, error: null }));

    try {
      const accounts = await wallet.request({
        method: 'eth_requestAccounts',
      });

      const chainId = await wallet.request({
        method: 'eth_chainId',
      });

      setWalletState({
        isConnected: true,
        address: accounts[0],
        chainId: parseInt(chainId, 16),
        isConnecting: false,
        error: null,
      });
    } catch (error: any) {
      console.error('Failed to connect wallet:', error);
      setWalletState(prev => ({
        ...prev,
        isConnecting: false,
        error: error.message || 'Failed to connect wallet.',
      }));
    }
  };

  const disconnect = async () => {
    if (!wallet) return;

    try {
      // Note: Coinbase Wallet doesn't have a standard disconnect method
      // We'll simulate disconnection by clearing our state
      setWalletState({
        isConnected: false,
        address: null,
        chainId: null,
        isConnecting: false,
        error: null,
      });
    } catch (error: any) {
      console.error('Failed to disconnect wallet:', error);
      setWalletState(prev => ({
        ...prev,
        error: error.message || 'Failed to disconnect wallet.',
      }));
    }
  };

  const switchChain = async (targetChainId: number) => {
    if (!wallet) return;

    try {
      await wallet.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: `0x${targetChainId.toString(16)}` }],
      });
    } catch (error: any) {
      // If the chain hasn't been added to the wallet, add it
      if (error.code === 4902) {
        const chainConfig = getChainConfig(targetChainId);
        if (chainConfig) {
          await wallet.request({
            method: 'wallet_addEthereumChain',
            params: [chainConfig],
          });
        }
      } else {
        throw error;
      }
    }
  };

  const contextValue: WalletContextType = {
    ...walletState,
    connect,
    disconnect,
    switchChain,
  };

  return (
    <WalletContext.Provider value={contextValue}>
      {children}
    </WalletContext.Provider>
  );
}

export function useCoinbaseWallet() {
  const context = useContext(WalletContext);
  if (context === undefined) {
    throw new Error('useCoinbaseWallet must be used within a CoinbaseWalletProvider');
  }
  return context;
}

// Helper function to get chain configuration
function getChainConfig(chainId: number) {
  const configs: Record<number, any> = {
    1: {
      chainId: '0x1',
      chainName: 'Ethereum Mainnet',
      nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
      rpcUrls: ['https://mainnet.infura.io/v3/'],
      blockExplorerUrls: ['https://etherscan.io'],
    },
    11155111: {
      chainId: '0xaa36a7',
      chainName: 'Sepolia Testnet',
      nativeCurrency: { name: 'Sepolia Ether', symbol: 'SEP', decimals: 18 },
      rpcUrls: ['https://sepolia.infura.io/v3/'],
      blockExplorerUrls: ['https://sepolia.etherscan.io'],
    },
    137: {
      chainId: '0x89',
      chainName: 'Polygon Mainnet',
      nativeCurrency: { name: 'MATIC', symbol: 'MATIC', decimals: 18 },
      rpcUrls: ['https://polygon-rpc.com/'],
      blockExplorerUrls: ['https://polygonscan.com'],
    },
  };

  return configs[chainId];
}