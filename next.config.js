/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { dev }) => {
    // Fix for ES module issues
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
    };

    // Completely exclude problematic files from Terser minification
    if (!dev && config.optimization && config.optimization.minimizer) {
      config.optimization.minimizer = config.optimization.minimizer.map((minimizer) => {
        if (minimizer.constructor.name === 'TerserPlugin') {
          return {
            ...minimizer,
            options: {
              ...minimizer.options,
              exclude: [
                /HeartbeatWorker\.js$/,
                /\.worker\.js$/,
                /static\/media\/HeartbeatWorker\./,
              ],
              terserOptions: {
                ...minimizer.options.terserOptions,
                parse: {
                  ...minimizer.options.terserOptions?.parse,
                  ecma: 2020,
                },
                compress: {
                  ...minimizer.options.terserOptions?.compress,
                  module: true,
                },
                mangle: {
                  ...minimizer.options.terserOptions?.mangle,
                  module: true,
                },
                format: {
                  ...minimizer.options.terserOptions?.format,
                  comments: false,
                },
              },
            }
          };
        }
        return minimizer;
      });
    }

    return config;
  },
  transpilePackages: ['@coinbase/wallet-sdk'],
  // Update ESLint configuration for Next.js 15
  eslint: {
    // Use modern ESLint configuration
    ignoreDuringBuilds: false,
  },
};

module.exports = nextConfig;