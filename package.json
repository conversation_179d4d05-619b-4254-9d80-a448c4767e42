{"name": "crefy-connect-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@coinbase/wallet-sdk": "^4.3.4", "@ensdomains/ensjs": "^4.0.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@react-three/drei": "^9.121.2", "@react-three/fiber": "^8.13.7", "@tanstack/react-query": "^5.80.10", "@types/bcryptjs": "^2.4.6", "@types/gsap": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "animejs": "^4.0.2", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "ethers": "^6.14.4", "framer-motion": "^12.19.1", "gsap": "^3.13.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.516.0", "next": "15.3.4", "next-themes": "^0.2.1", "nodemailer": "^7.0.3", "ogl": "^1.0.11", "raw-loader": "^4.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.58.1", "react-parallax-tilt": "^1.7.298", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.8.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "three": "^0.150.1", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/prop-types": "^15.7.15", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^8.57.1", "eslint-config-next": "14.2.5", "eslint-plugin-react": "^7.37.5", "globals": "^16.2.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5", "typescript-eslint": "^8.34.1", "worker-loader": "^3.0.8"}}